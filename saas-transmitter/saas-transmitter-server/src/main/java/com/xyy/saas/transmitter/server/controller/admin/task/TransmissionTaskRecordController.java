package com.xyy.saas.transmitter.server.controller.admin.task;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordRespVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordSaveReqVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRetryReqVO;
import com.xyy.saas.transmitter.server.service.task.TransmissionTaskRecordService;
import com.xyy.saas.transmitter.server.service.transmission.TransmissionRetryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 数据传输-记录")
@RestController
@RequestMapping("/transmitter/transmission-task-record")
@Validated
public class TransmissionTaskRecordController {

    @Resource
    private TransmissionTaskRecordService transmissionTaskRecordService;

    @Resource
    private TransmissionRetryService transmissionRetryService;

    @PostMapping("/create")
    @Operation(summary = "创建数据传输-记录")
    @PreAuthorize("@ss.hasPermission('saas:transmission-task-record:create')")
    public CommonResult<Long> createTransmissionTaskRecord(@Valid @RequestBody TransmissionTaskRecordSaveReqVO createReqVO) {
        return success(transmissionTaskRecordService.createTransmissionTaskRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新数据传输-记录")
    @PreAuthorize("@ss.hasPermission('saas:transmission-task-record:update')")
    public CommonResult<Boolean> updateTransmissionTaskRecord(@Valid @RequestBody TransmissionTaskRecordSaveReqVO updateReqVO) {
        transmissionTaskRecordService.updateTransmissionTaskRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除数据传输-记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:transmission-task-record:delete')")
    public CommonResult<Boolean> deleteTransmissionTaskRecord(@RequestParam("id") Long id) {
        transmissionTaskRecordService.deleteTransmissionTaskRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得数据传输-记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:transmission-task-record:query')")
    public CommonResult<TransmissionTaskRecordRespVO> getTransmissionTaskRecord(@RequestParam("id") Long id) {
        return success(transmissionTaskRecordService.getTransmissionTaskRecord(id));
    }

    @GetMapping("/page")
    @Operation(summary = "获得数据传输-记录分页")
    @PreAuthorize("@ss.hasPermission('saas:transmission-task-record:query')")
    public CommonResult<PageResult<TransmissionTaskRecordRespVO>> getTransmissionTaskRecordPage(@Valid TransmissionTaskRecordPageReqVO pageReqVO) {
        return success(transmissionTaskRecordService.getTransmissionTaskRecordPage(pageReqVO));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出数据传输-记录 Excel")
    @PreAuthorize("@ss.hasPermission('saas:transmission-task-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTransmissionTaskRecordExcel(@Valid TransmissionTaskRecordPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TransmissionTaskRecordRespVO> list = transmissionTaskRecordService.getTransmissionTaskRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "数据传输-记录.xls", "数据", TransmissionTaskRecordRespVO.class,
            list);
    }

    @PostMapping("/retry")
    @Operation(summary = "重传数据传输任务")
    @PreAuthorize("@ss.hasPermission('saas:transmission-task-record:retry')")
    public CommonResult<Boolean> retryTransmissionTasks(@RequestBody TransmissionTaskRetryReqVO taskRetryReqVO) {
        transmissionRetryService.retryContractInvokeAsync(taskRetryReqVO);
        return success(true);
    }

}