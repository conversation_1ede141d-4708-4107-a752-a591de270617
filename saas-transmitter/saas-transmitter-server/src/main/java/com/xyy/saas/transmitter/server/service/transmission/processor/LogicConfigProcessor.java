package com.xyy.saas.transmitter.server.service.transmission.processor;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_NOT_SUPPORT_NODE_TYPE;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_NOT_SUPPORT_ORGAN_TYPE;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_POST_PROCESS_FAILED;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_PROTOCOL_CONFIG_NOT_EXISTS;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_REQ_VALIDATION_FAILED;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_SERVICE_PACK_NOT_EXISTS;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_TASK_CREATE_FAILED;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.formatError;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.xyy.saas.inquiry.enums.transmitter.DslTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.RequestStatusEnum;
import com.xyy.saas.inquiry.enums.transmitter.RetryStrategyEnum;
import com.xyy.saas.transmitter.api.config.dto.TransmissionConfigItemDTO;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigItemPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordRespVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordSaveReqVO;
import com.xyy.saas.transmitter.server.convert.config.TransmissionConfigItemConvert;
import com.xyy.saas.transmitter.server.convert.transmission.TransmissionConvert;
import com.xyy.saas.transmitter.server.dal.mysql.config.TransmissionConfigItemMapper;
import com.xyy.saas.transmitter.server.service.task.TransmissionTaskRecordService;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.LogicValidationConfig;
import jakarta.annotation.Resource;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.CronExpression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;

/**
 * 逻辑配置处理器 - 领域服务 负责处理业务节点的逻辑配置和任务生命周期管理
 * <p>
 * 核心职责： 1. 前置处理：执行参数校验和前置过滤 2. 任务创建：构建和管理任务实体 3. 后置处理：执行业务规则和数据转换
 * <p>
 * 处理流程： 1. 创建处理上下文 2. 执行前置处理(参数校验、前置过滤) 3. 创建任务(主任务、下游任务) 4. 执行后置处理(参数填充、业务处理)
 */
@Slf4j
public abstract class LogicConfigProcessor {

    private static final ObjectMapper objectMapper = new ObjectMapper(new YAMLFactory())
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    // 注入的依赖
    @Resource
    protected TransmissionConfigItemMapper configItemMapper;
    @Resource
    protected TransmissionTaskRecordService taskService;

    private final TaskExecutionTimeHandler timeHandler = new TaskExecutionTimeHandler();
    private final BusinessExceptionHandler exceptionHandler = new BusinessExceptionHandler();

    /**
     * 前置处理 执行参数校验和前置过滤，确保请求数据的合法性
     * <p>
     * 处理流程： 1. 创建处理上下文 2. 执行前置过滤 3. 前置参数组装 4. 执行参数校验
     *
     * @param transmissionReqDTO 传输请求对象
     * @param servicePack        服务包信息
     * @return 处理结果
     */
    public final CommonResult<ProcessContext> preProcess(TransmissionReqDTO transmissionReqDTO,
        TransmissionServicePackDTO servicePack) {
        log.debug("[preProcess][开始前置处理] tenantId:{} nodeType:{}",
            transmissionReqDTO.getConfig().getTenantId(),
            transmissionReqDTO.getConfig().getNodeType().getDesc());

        try {
            // 1.初始化逻辑配置处理上下文
            ProcessContext context = createContext(transmissionReqDTO, servicePack);
            log.debug("[preProcess][创建上下文完成] {}", context.getLogContext());

            // 2. 前置过滤
            if (!preFilter(transmissionReqDTO, context.getLogicConfig().getPreFilter())) {
                log.debug("[preProcess][前置过滤未通过] {}", context.getLogContext());
                return CommonResult.success(null);
            }
            log.debug("[preProcess][前置过滤通过] {}", context.getLogContext());

            // 3.前置参数组装
            fillPreProcessParameters(transmissionReqDTO, context.getLogicConfig().getPreParameter());

            // 4. 前置组装完参数后过滤
            if (!preParameterFilter(transmissionReqDTO, context.getLogicConfig().getPreParameterFilter())) {
                log.debug("[preProcess][前置组装完参数后过滤未通过] {}", context.getLogContext());
                return CommonResult.success(null);
            }

            // 5. 参数校验
            validateParameters(transmissionReqDTO, context.getLogicConfig().getPreValidation());
            log.debug("[preProcess][参数校验通过] {}", context.getLogContext());

            log.debug("[preProcess][前置处理完成] {}", context.getLogContext());
            return CommonResult.success(context);
        } catch (Exception e) {
            log.error("[preProcess][前置处理异常] tenantId:{} error:{}",
                transmissionReqDTO.getConfig().getTenantId(), e.getMessage(), e);
            return exceptionHandler.handlePreProcessException(transmissionReqDTO, e);
        }
    }

    /**
     * 创建任务 根据业务配置创建传输任务
     * <p>
     * 处理流程： 1. 构建主任务 2. 保存主任务 3. 构建下游任务 4. 保存下游任务
     *
     * @param context 处理上下文
     * @return 处理结果
     */
    public final CommonResult<ProcessContext> createTask(ProcessContext context) {
        log.debug("[createTask][开始创建任务] {}", context.getLogContext());

        try {
            TaskBuilder taskBuilder = new TaskBuilder(context);

            // 1. 构建主任务
            TransmissionTaskRecordSaveReqVO mainTask = taskBuilder.buildMainTask();
            log.debug("[createTask][主任务构建完成] {} taskId:{}", context.getLogContext(), mainTask.getId());

            // 2. 构建下游任务
            List<TransmissionTaskRecordSaveReqVO> downstreamTasks = taskBuilder.buildDownstreamTasks();
            if (!downstreamTasks.isEmpty()) {
                log.debug("[createTask][下游任务构建完成] {} mainTaskId:{} downstreamCount:{}",
                    context.getLogContext(), mainTask.getId(), downstreamTasks.size());
            }

            // 3. 保存任务
            taskService.createOrUpdateTask(mainTask, downstreamTasks);

            // 4. 构建下游请求入参
            downstreamTasks.forEach(taskBuilder::addDownstreamRequest);

            // 5. 设置主任务
            context.setTaskRecord(mainTask);

            log.debug("[createTask][任务创建完成] {} mainTaskId:{}", context.getLogContext(), mainTask.getId());

            return CommonResult.success(context);
        } catch (Exception e) {
            log.error("[createTask][创建任务异常] {} error:{}",
                context.getLogContext(), e.getMessage(), e);
            return exceptionHandler.handleTaskCreateException(context.getReqDTO(), e);
        }
    }

    /**
     * 后置处理 执行业务规则和数据转换
     * <p>
     * 处理流程： 1. 参数填充 2. 业务规则校验 3. 数据转换处理 4. 结果封装
     *
     * @param context 处理上下文
     * @return 处理结果
     */
    public final CommonResult<ProcessContext> postProcess(ProcessContext context) {
        TransmissionTaskRecordSaveReqVO taskRecord = context.getTaskRecord();
        log.debug("[postProcess][开始后置处理] {} taskId:{}", context.getLogContext(), taskRecord.getId());

        try {
            LogicConfig logicConfig = context.getLogicConfig();

            // 1. 参数填充
            fillPostProcessParameters(context.getReqDTO(), logicConfig.getPostParameter());
            log.debug("[postProcess][参数填充完成] {}", context.getLogContext());

            // 2. 后置校验
            postValidate(context.getReqDTO(), logicConfig.getPostValidation());
            log.debug("[postProcess][后置校验通过] {}", context.getLogContext());

            // 3. 业务逻辑处理
            processLogicConfig(context.getReqDTO(), taskRecord, logicConfig.getLogicValidation(),
                context.getServicePack());
            log.debug("[postProcess][后置处理完成] {} taskId:{}", context.getLogContext(), taskRecord.getId());

            return CommonResult.success(context);
        } catch (Exception e) {
            log.error("[postProcess][后置处理异常] {} taskId:{} error:{}",
                context.getLogContext(), taskRecord.getId(), e.getMessage(), e);
            return exceptionHandler.handlePostProcessException(context.getReqDTO(), taskRecord, e);
        }
    }

    /**
     * 处理上下文 - 值对象 负责存储和管理处理过程中的状态和数据
     * <p>
     * 主要职责： 1. 存储请求数据：保存原始请求和配置信息 2. 维护处理状态：跟踪处理过程的各个阶段 3. 提供上下文信息：支持处理过程中的数据访问
     * <p>
     * 设计原则： 1. 不可变性：创建后状态不可修改 2. 完整性：包含处理所需的所有信息 3. 独立性：与具体处理逻辑解耦
     * <p>
     * 使用场景： 1. 参数校验：提供配置和请求数据 2. 任务创建：提供任务所需信息 3. 结果处理：存储处理结果
     */
    @Data
    @Builder
    public static class ProcessContext {

        private final Long tenantId; // 租户ID
        private final TransmissionReqDTO reqDTO; // 请求数据
        private final TransmissionServicePackDTO servicePack; // 服务包信息
        private final TransmissionConfigItemDTO protocolConfigItem; // 协议配置项
        private final LogicConfig logicConfig; // 逻辑配置
        private TransmissionTaskRecordSaveReqVO taskRecord;

        /**
         * 获取日志上下文 用于统一的日志输出格式
         */
        public String getLogContext() {
            return String.format("tenantId:%d nodeType:%s",
                tenantId, reqDTO.getConfig().getNodeType().getDesc());
        }

        /**
         * 检查是否存在下游节点
         */
        public boolean hasDownstreamNodes() {
            return logicConfig != null
                && logicConfig.getDependency() != null
                && !CollectionUtils.isEmpty(logicConfig.getDependency().getDownstreamNodes());
        }

        /**
         * 创建处理上下文 工厂方法：从配置项中解析并构建上下文对象
         *
         * @param configItems 配置项列表
         * @return 处理上下文对象
         */
        public static ProcessContext create(TransmissionReqDTO reqDTO,
            TransmissionServicePackDTO servicePack,
            List<TransmissionConfigItemDTO> configItems) {
            // 1. 获取并解析配置
            TransmissionConfigItemDTO protocolConfigItem = findConfigItem(configItems, DslTypeEnum.PROTOCOL);
            LogicConfig logicConfig = getLogicConfig(configItems);

            // 2. 构建上下文
            return ProcessContext.builder()
                .tenantId(reqDTO.getConfig().getTenantId())
                .reqDTO(reqDTO)
                .servicePack(servicePack)
                .protocolConfigItem(protocolConfigItem)
                .logicConfig(logicConfig)
                .build();
        }

        /**
         * 获取逻辑配置 从配置项列表中查找并解析逻辑配置信息
         */
        private static LogicConfig getLogicConfig(List<TransmissionConfigItemDTO> configItems) {
            TransmissionConfigItemDTO logicConfigItem = findConfigItem(configItems, DslTypeEnum.LOGIC);
            return parseLogicConfig(logicConfigItem.getConfigValue());
        }

        /**
         * 查找配置项 从配置项列表中查找指定类型的配置
         * <p>
         * 查找规则： 1. 匹配DSL类型 2. 返回首个匹配项 3. 未找到时返回空配置项
         */
        private static TransmissionConfigItemDTO findConfigItem(
            List<TransmissionConfigItemDTO> configItems, DslTypeEnum dslType) {
            if (CollectionUtils.isEmpty(configItems) || dslType == null) {
                return new TransmissionConfigItemDTO();
            }
            return configItems.stream()
                .filter(item -> dslType.getCode().equals(item.getDslType()))
                .findFirst()
                .orElse(new TransmissionConfigItemDTO());
        }

        /**
         * 解析逻辑配置 将YAML格式的配置字符串解析为逻辑配置对象
         * <p>
         * 解析规则： 1. 空值处理：返回默认配置 2. 使用YAML解析器转换 3. 异常时返回默认配置
         */
        private static LogicConfig parseLogicConfig(String configValue) {
            if (StringUtils.isBlank(configValue)) {
                return new LogicConfig();
            }
            try {
                return objectMapper.readValue(configValue, LogicConfig.class);
                // return new Yaml().loadAs(configValue, LogicConfig.class);
            } catch (Exception e) {
                log.error("[parseLogicConfig][解析逻辑配置失败] configValue:{}", configValue, e);
                return new LogicConfig();
            }
        }

        /**
         * 验证上下文的完整性 确保必要的配置项存在
         *
         * @throws IllegalArgumentException 当必要配置缺失时
         */
        public void validate() {
            if (protocolConfigItem == null) {
                throw exception(TRANSMISSION_PROTOCOL_CONFIG_NOT_EXISTS);
            }
            // 可以添加其他必要的验证
        }
    }

    /**
     * 逻辑配置 - 值对象 封装节点的处理逻辑配置信息
     * <p>
     * 主要职责： 1. 前置处理配置：过滤条件和参数设置 2. 校验规则配置：参数验证规则 3. 执行配置：任务执行相关设置 4. 依赖配置：节点间的依赖关系
     * <p>
     * 配置项： 1. 重试策略：重试次数和间隔 2. 执行时间：延迟和定时配置 3. 校验规则：参数校验配置 4. 依赖关系：上下游节点配置
     */
    @Data
    @Accessors(chain = false)
    public static class LogicConfig {

        /**
         * 重试配置
         * <p>
         * 功能说明： 1. 控制任务失败后的重试行为 2. 定义重试策略和执行规则
         * <p>
         * 配置项： 1. allowRetry：是否允许重试，默认true 2. maxRetryCount：最大重试次数，默认0 3. retryInterval：重试间隔(秒)，默认5 4. retryStrategy：重试策略，默认FIXED
         * <p>
         * 初始化说明： 1. 字段声明时直接初始化，确保配置对象不为空 2. JSON反序列化时，缺失的属性会使用默认值 3. 无需额外的空值判断和初始化处理
         */
        private RetryConfig retryConfig = new RetryConfig();

        /**
         * 前置过滤配置 用于过滤不需要处理的请求
         * <p>
         * 配置内容： 1. 过滤条件：定义请求过滤的规则 2. 过滤表达式：具体的过滤逻辑 3. 过滤参数：过滤所需的参数
         */
        private FilterConfig preFilter = new FilterConfig();

        /**
         * 前置参数配置 用于组装和准备请求参数
         * <p>
         * 配置内容： 1. 参数映射：源字段到目标字段的映射关系 2. 参数转换：数据类型和格式转换规则 3. 默认值：参数的默认值设置
         */
        private PreParameterConfig preParameter = new PreParameterConfig();

        /**
         * 前置组装参数完成后 过滤配置 用于过滤不需要处理的请求
         * <p>
         * 配置内容： 1. 过滤条件：定义请求过滤的规则 2. 过滤表达式：具体的过滤逻辑 3. 过滤参数：过滤所需的参数
         */
        private FilterConfig preParameterFilter = new FilterConfig();


        /**
         * 前置校验配置 用于验证请求参数的合法性
         * <p>
         * 配置内容： 1. 必填校验：必须提供的字段 2. 格式校验：数据格式的验证规则 3. 业务校验：特定业务规则的验证
         */
        private PreValidationConfig preValidation = new PreValidationConfig();

        /**
         * 执行时间配置 用于控制任务的执行时间
         * <p>
         * 配置内容： 1. 执行时间：任务的预期执行时间 2. 超时设置：任务的超时限制 3. 重试策略：失败后的重试时间间隔
         */
        private ExecutionTimeConfig executionTime = new ExecutionTimeConfig();

        /**
         * 业务逻辑配置 用于定义具体的业务处理规则
         * <p>
         * 配置内容： 1. 处理规则：业务处理的具体规则 2. 转换规则：数据转换的规则 3. 处理流程：业务处理的步骤定义
         */
        private LogicValidationConfig logicValidation = new LogicValidationConfig();

        /**
         * 后置参数配置 用于处理响应数据的转换和组装
         * <p>
         * 配置内容： 1. 响应映射：响应字段的映射关系 2. 数据转换：响应数据的转换规则 3. 结果组装：最终响应数据的组装规则
         */
        private PostParameterConfig postParameter = new PostParameterConfig();

        /**
         * 后置校验配置 用于验证处理结果的正确性
         * <p>
         * 配置内容： 1. 结果校验：处理结果的验证规则 2. 数据完整性：结果数据的完整性检查 3. 业务规则：特定业务场景的结果验证
         */
        private PostValidationConfig postValidation = new PostValidationConfig();

        /**
         * 依赖配置 用于定义节点间的依赖关系
         * <p>
         * 配置内容： 1. 上游节点：当前节点依赖的节点 2. 下游节点：依赖当前节点的节点 3. 依赖规则：节点间的依赖处理规则
         */
        private DependencyConfig dependency = new DependencyConfig();

        @Data
        public static class RetryConfig {

            /**
             * 是否允许重试 控制任务失败后是否进行重试
             * <p>
             * 使用场景： 1. 网络超时：临时性网络问题导致的失败 2. 服务不可用：目标服务暂时不可用 3. 数据锁定：资源暂时被锁定
             */
            private boolean allowRetry = true;

            /**
             * 最大重试次数 限制任务重试的最大次数
             * <p>
             * 配置说明： 1. 0：不进行重试 2. >0：最大重试次数 3. -1：无限重试(不建议)
             * <p>
             * 默认值：0（不重试）
             */
            private int maxRetryCount = 0;

            /**
             * 重试间隔时间（秒） 两次重试之间的等待时间
             * <p>
             * 配置说明： 1. 固定间隔：所有重试使用相同的间隔时间 2. 递增间隔：每次重试增加等待时间（如：5s、10s、20s）
             * <p>
             * 默认值：5（秒）
             */
            private int retryInterval = 5;

            /**
             * 重试策略 定义重试的具体执行策略
             * <p>
             * 策略类型： 1. FIXED：固定间隔重试 2. INCREMENTAL：递增间隔重试 3. EXPONENTIAL：指数退避重试
             * <p>
             * 默认值：FIXED（固定间隔）
             */
            private RetryStrategyEnum retryStrategy = RetryStrategyEnum.FIXED;
        }

        @Data
        public static class FilterConfig {

            /**
             * 过滤条件表达式 用于定义具体的过滤规则
             */
            private String condition;

            /**
             * 过滤参数列表 过滤条件中使用的参数定义
             */
            private Map<String, String> parameters;

            /**
             * 是否启用过滤 控制是否执行过滤逻辑
             */
            private boolean enabled = true;
        }

        @Data
        public static class PreParameterConfig {

            /**
             * 组装数据节点列表（比如、租户信息、商品信息等） 需要在子类定义枚举以及查询方法
             */
            private List<String> nodes;

            /**
             * 参数映射关系 源字段到目标字段的映射定义
             */
            private Map<String, String> mappings;

            /**
             * 参数转换规则 定义字段值的转换逻辑
             */
            private Map<String, String> transformations;

            /**
             * 重试时是否重新组装参数 控制重试时是否需要重新处理请求参数
             * <p>
             * 使用场景： 1. true：需要重新获取或计算某些参数（如时间戳、随机数等） 2. false：直接使用原始参数重试
             * <p>
             * 默认值：true（重新组装参数）
             */
            private boolean reassembleParams = true;
        }

        @Data
        public static class PreValidationConfig {

            /**
             * 自定义校验规则 特定业务场景的校验规则
             */
            private List<String> validations;

            /**
             * 是否全匹配（经过validations后是否需要全满足）
             */
            private boolean isAllMatch = true;
        }

        @Data
        public static class ExecutionTimeConfig {

            /**
             * 是否仅生成任务不上传监管
             */
            private boolean onlyTask;

            /**
             * 延迟执行时间（秒） 任务延迟执行的时间
             */
            private Integer delay;

            /**
             * 定时执行表达式 Cron表达式定义的执行时间
             */
            private String cron;

            /**
             * 超时时间（秒） 任务执行的超时限制
             */
            private Integer timeout;
        }

        @Data
        public static class LogicValidationConfig {

            /**
             * 业务规则列表 定义具体的业务处理规则
             */
            private List<String> rules;

            /**
             * 数据转换规则 业务处理中的数据转换规则
             */
            private Map<String, String> transformations;

            /**
             * 处理步骤配置 业务处理的步骤定义
             */
            private List<String> steps;
        }

        @Data
        public static class PostParameterConfig {

            /**
             * 组装数据节点列表（比如、租户信息、商品信息等） 需要在子类定义枚举以及查询方法
             */
            private List<String> nodes;

            /**
             * 响应字段映射 处理结果字段的映射关系
             */
            private Map<String, String> responseMappings;

            /**
             * 响应数据转换 处理结果的数据转换规则
             */
            private Map<String, String> transformations;

            /**
             * 结果组装规则 最终响应数据的组装规则
             */
            private List<String> assemblyRules;

            /**
             * 重试时是否重新组装参数 控制重试时是否需要重新处理请求参数
             * <p>
             * 使用场景： 1. true：需要重新获取或计算某些参数（如时间戳、随机数等） 2. false：直接使用原始参数重试
             * <p>
             * 默认值：true（重新组装参数）
             */
            private boolean reassembleParams = true;
        }

        @Data
        public static class PostValidationConfig {

            /**
             * 自定义校验规则 特定业务场景的校验规则
             */
            private List<String> validations;

            /**
             * 完整性校验配置 结果数据的完整性检查规则
             */
            private Map<String, String> integrityChecks;

            /**
             * 业务规则校验 特定业务场景的结果验证规则
             */
            private List<String> businessRules;
        }

        @Data
        public static class DependencyConfig {

            /**
             * 下游节点配置 依赖当前节点的节点列表
             */
            private List<Integer> downstreamNodes;

            /**
             * 依赖处理规则 节点间依赖关系的处理规则
             */
            private Map<String, String> dependencyRules;
        }
    }

    /**
     * 创建处理上下文 工厂方法：构建并验证上下文对象
     *
     * @throws IllegalArgumentException 当必要参数缺失时
     */
    private ProcessContext createContext(TransmissionReqDTO transmissionReqDTO,
        TransmissionServicePackDTO servicePack) {
        if (transmissionReqDTO == null || servicePack == null
            || CollectionUtils.isEmpty(servicePack.getConfigPackage().getConfigItems())) {
            throw exception(TRANSMISSION_SERVICE_PACK_NOT_EXISTS);
        }

        ProcessContext context = ProcessContext.create(transmissionReqDTO, servicePack,
            servicePack.getConfigPackage().getConfigItems());
        context.validate();
        // 子类先处理序列化具象类
        serializeObj(transmissionReqDTO);
        return context;
    }

    // 子类可选择实现的方法

    /**
     * 子类在处理任务前，转换序列化成相关的类，方便后续取值组装，参数校验等
     *
     * @param transmissionReqDTO 传输请求对象
     */
    protected void serializeObj(TransmissionReqDTO transmissionReqDTO) {

    }

    /**
     * 参数校验 验证请求参数的合法性
     *
     * @param transmissionReqDTO 传输请求对象
     * @param config             校验配置
     * @throws IllegalArgumentException 当参数验证失败时抛出
     */
    protected void validateParameters(TransmissionReqDTO transmissionReqDTO,
        LogicConfig.PreValidationConfig config) {

    }

    /**
     * 前置参数填充 在业务处理前填充必要的实时数据（如快照数据）
     * <p>
     * 处理内容： 1. 填充实时数据 2. 组装快照信息 3. 转换请求参数
     *
     * @param transmissionReqDTO 传输请求对象
     * @param config             前置参数配置
     */
    protected void fillPreProcessParameters(TransmissionReqDTO transmissionReqDTO,
        LogicConfig.PreParameterConfig config) {

        // 重试时无需重新填充参数
        if (transmissionReqDTO.isRetry() && !config.isReassembleParams()) {
            return;
        }

        // todo 填充实时数据
    }

    /**
     * 后置参数填充 在业务处理后进行数据转换和结果封装
     * <p>
     * 处理内容： 1. 结果数据转换 2. 响应参数组装 3. 扩展信息填充
     *
     * @param transmissionReqDTO 传输请求对象
     * @param config             后置参数配置
     */
    protected void fillPostProcessParameters(TransmissionReqDTO transmissionReqDTO,
        LogicConfig.PostParameterConfig config) {

        // 重试时无需重新填充参数
        if (transmissionReqDTO.isRetry() && !config.isReassembleParams()) {
            return;
        }

        // todo 填充实时数据

        // todo 填充到task中
    }

    // 子类可选择性重写的方法

    /**
     * 前置过滤 根据条件判断是否继续处理请求
     */
    protected boolean preFilter(TransmissionReqDTO transmissionReqDTO,
        LogicConfig.FilterConfig filterConfig) {
        return evaluateCondition(transmissionReqDTO, filterConfig);
    }

    /**
     * 前置填充完参数后校验
     *
     * @param transmissionReqDTO
     * @param filterConfig
     * @return
     */
    protected boolean preParameterFilter(TransmissionReqDTO transmissionReqDTO,
        LogicConfig.FilterConfig filterConfig) {
        return evaluateCondition(transmissionReqDTO, filterConfig);
    }

    /**
     * 后置校验 在参数填充后进行校验
     */
    protected void postValidate(TransmissionReqDTO transmissionReqDTO,
        LogicConfig.PostValidationConfig config) {
    }

    /**
     * 业务逻辑处理 执行具体的业务逻辑
     */
    protected void processLogicConfig(TransmissionReqDTO transmissionReqDTO,
        TransmissionTaskRecordSaveReqVO task,
        LogicValidationConfig config,
        TransmissionServicePackDTO servicePack) {
    }

    /**
     * 条件表达式求值 使用SpEL表达式引擎评估过滤条件
     * <p>
     * 处理流程： 1. 创建表达式解析器 2. 设置评估上下文 3. 执行条件表达式
     *
     * @param transmissionReqDTO 请求对象，用于条件评估
     * @param filterConfig       过滤配置
     * @return 条件评估结果
     */
    private boolean evaluateCondition(TransmissionReqDTO transmissionReqDTO, LogicConfig.FilterConfig filterConfig) {
        if (filterConfig == null || StringUtils.isBlank(filterConfig.getCondition())) {
            return true;
        }
        try {
            ExpressionParser parser = new SpelExpressionParser();
            return Boolean.TRUE.equals(parser.parseExpression(filterConfig.getCondition())
                .getValue(transmissionReqDTO, Boolean.class));
        } catch (Exception e) {
            log.error("[evaluateCondition][条件表达式解析失败] condition:{}", filterConfig.getCondition(), e);
            return false;
        }
    }

    /**
     * 判断上游任务是否完成 检查上游任务的执行状态
     * <p>
     * 完成条件： 1. 任务存在且不为空 2. 任务状态为成功完成
     *
     * @param task 上游任务记录
     * @return true-已完成，false-未完成
     */
    private boolean isUpstreamTaskCompleted(TransmissionTaskRecordRespVO task) {
        return task != null && RequestStatusEnum.SUCCESS.getCode() == task.getRequestStatus();
    }

    /**
     * 任务执行时间处理器 - 领域服务 负责处理任务的执行时间相关逻辑
     * <p>
     * 主要职责： 1. 解析时间配置：处理延迟和定时配置 2. 计算执行时间：确定任务的具体执行时间 3. 验证时间有效性：确保时间配置的正确性
     * <p>
     * 处理逻辑： 1. 优先使用延迟配置 2. 其次使用定时配置 3. 默认立即执行
     */
    private class TaskExecutionTimeHandler {

        /**
         * 处理任务执行时间 根据配置设置任务的预期执行时间
         *
         * @param taskRecord 任务记录
         * @param timeConfig 时间配置
         */
        void handle(TransmissionTaskRecordSaveReqVO taskRecord, LogicConfig.ExecutionTimeConfig timeConfig, TransmissionReqDTO reqDTO) {

            handleExecution(taskRecord, timeConfig, reqDTO);

            if (timeConfig.getDelay() != null) {
                taskRecord.setExpectedTime(LocalDateTime.now().plusSeconds(timeConfig.getDelay()));
                return;
            }

            if (StringUtils.isNotBlank(timeConfig.getCron())) {
                handleCronExecution(taskRecord, timeConfig.getCron());
            }

            // todo 后续是新启一个线程扫描taskRecord表扫描还是发延迟消息，待确认
        }

        /**
         * 处理执行的任务状态 如果存在上游任务，需要等待上游任务完成
         */
        private void handleExecution(TransmissionTaskRecordSaveReqVO taskRecord, LogicConfig.ExecutionTimeConfig timeConfig, TransmissionReqDTO reqDTO) {
            if (taskRecord.getUpstreamTaskId() == null) {
                // 设置状态为请求中 或者 未请求
                taskRecord.setExpectedTime(LocalDateTime.now());
                taskRecord.setRequestStatus(timeConfig.onlyTask && !reqDTO.isRetry() ? RequestStatusEnum.NOT_REQUESTED.getCode() : RequestStatusEnum.IN_PROGRESS.getCode());
                return;
            }

            TransmissionTaskRecordRespVO upstreamTask = taskService
                .getTransmissionTaskRecord(taskRecord.getUpstreamTaskId());
            if (isUpstreamTaskCompleted(upstreamTask)) {
                taskRecord.setExpectedTime(LocalDateTime.now());
                taskRecord.setRequestStatus(RequestStatusEnum.IN_PROGRESS.getCode());
            }

        }

        /**
         * 处理Cron定时任务 解析Cron表达式并设置下次执行时间
         */
        private void handleCronExecution(TransmissionTaskRecordSaveReqVO taskRecord, String cron) {
            try {
                CronExpression cronExpression = new CronExpression(cron);
                Date nextValidTime = cronExpression.getNextValidTimeAfter(new Date());
                if (nextValidTime != null) {
                    taskRecord.setExpectedTime(LocalDateTime.ofInstant(
                        nextValidTime.toInstant(), ZoneId.systemDefault()));
                }
            } catch (ParseException e) {
                log.error("[handleCronExecution][Cron表达式解析失败] cron:{}", cron, e);
            }
        }
    }

    /**
     * 业务异常处理器 - 领域服务 负责统一处理业务处理过程中的异常
     * <p>
     * 主要职责： 1. 异常分类：识别和分类不同类型的异常 2. 错误转换：将异常转换为统一的响应格式 3. 异常记录：记录异常信息便于问题排查
     * <p>
     * 处理原则： 1. 统一处理：提供一致的异常处理方式 2. 信息完整：保留完整的异常上下文 3. 优雅降级：提供合适的降级处理
     */
    private class BusinessExceptionHandler {

        /**
         * 处理前置处理异常 统一处理前置处理阶段的异常
         *
         * @param reqDTO 请求数据
         * @param e      异常信息
         * @return 错误响应
         */
        CommonResult<ProcessContext> handlePreProcessException(TransmissionReqDTO reqDTO, Exception e) {
            ErrorCode error = formatError(TRANSMISSION_REQ_VALIDATION_FAILED, e);
            log.error("[preProcess][参数校验失败] tenantId:{} error:{}",
                reqDTO.getConfig().getTenantId(), error.getMsg(), e);
            return CommonResult.error(error);
        }

        /**
         * 处理任务创建异常 处理任务创建和保存过程中的异常
         */
        CommonResult<ProcessContext> handleTaskCreateException(
            TransmissionReqDTO reqDTO, Exception e) {
            ErrorCode error = formatError(TRANSMISSION_TASK_CREATE_FAILED, e);
            log.error("[createTask][创建任务失败] tenantId:{} error:{}",
                reqDTO.getConfig().getTenantId(), error.getMsg(), e);
            return CommonResult.error(error);
        }

        /**
         * 处理后置处理异常 处理业务规则执行和数据处理过程中的异常
         */
        CommonResult<ProcessContext> handlePostProcessException(
            TransmissionReqDTO reqDTO,
            TransmissionTaskRecordSaveReqVO taskRecord,
            Exception e) {
            ErrorCode error = formatError(TRANSMISSION_POST_PROCESS_FAILED, e);
            log.error("[postProcess][后置处理失败] taskId:{} error:{}",
                taskRecord.getId(), error.getMsg(), e);

            updateTaskFailed(taskRecord, error.getMsg());
            return CommonResult.error(error);
        }

        /**
         * 更新任务失败状态 处理任务执行失败的状态更新
         * <p>
         * 更新内容： 1. 设置失败状态 2. 记录错误信息 3. 持久化状态变更
         *
         * @param taskRecord 任务记录
         * @param errorMsg   错误信息
         */
        private void updateTaskFailed(TransmissionTaskRecordSaveReqVO taskRecord, String errorMsg) {
            try {
                taskRecord.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                taskRecord.setErrorMessage(errorMsg);
                taskService.updateTransmissionTaskRecord(taskRecord);
            } catch (Exception ex) {
                log.error("[updateTaskFailed][更新任务状态失败] taskId:{} error:{}",
                    taskRecord.getId(), ex.getMessage(), ex);
            }
        }
    }

    /**
     * 任务构建器 - 值对象 负责构建和管理传输任务实体
     * <p>
     * 主要职责： 1. 构建主任务：创建当前节点的任务实体 2. 构建下游任务：根据依赖配置创建关联任务 3. 设置任务属性：处理任务的基础属性和扩展信息 4. 管理任务关系：处理主任务和下游任务的关联关系
     * <p>
     * 设计原则： 1. 单一职责：专注于任务实体的构建和管理 2. 封装变化：隔离任务创建的复杂性 3. 依赖倒置：通过上下文获取配置信息
     * <p>
     * 使用场景： 1. 创建主任务：处理当前节点的任务 2. 创建下游任务：处理依赖节点的任务 3. 构建任务关系：维护任务间的依赖关系
     */
    private class TaskBuilder {

        private final ProcessContext context;

        TaskBuilder(ProcessContext context) {
            this.context = context;
        }

        /**
         * 构建主任务 创建当前节点的主任务实体，并设置相关属性
         * <p>
         * 处理流程： 1. 创建基础任务实体 2. 设置执行时间配置 3. 返回完整的任务对象
         *
         * @return 主任务实体
         */
        TransmissionTaskRecordSaveReqVO buildMainTask() {
            TransmissionTaskRecordSaveReqVO taskRecord = createTask(context.getProtocolConfigItem());
            timeHandler.handle(taskRecord, context.getLogicConfig().getExecutionTime(), context.getReqDTO());
            return taskRecord;
        }

        /**
         * 构建下游任务 根据依赖配置创建下游节点的任务实体
         * <p>
         * 处理流程： 1. 检查是否存在下游节点 2. 查询下游配置项 3. 过滤并创建下游任务
         *
         * @return 下游任务列表
         */
        List<TransmissionTaskRecordSaveReqVO> buildDownstreamTasks() {
            if (!context.hasDownstreamNodes()) {
                return Collections.emptyList();
            }

            return queryDownstreamConfigItems()
                .stream()
                .filter(this::isProtocolConfig)
                .map(c -> TransmissionConvert.INSTANCE.createTask(c, context).setId(null))
                .collect(Collectors.toList());
        }

        /**
         * 查询下游配置项 根据依赖关系获取下游节点的配置信息
         *
         * @return 下游节点配置列表
         */
        private List<TransmissionConfigItemDTO> queryDownstreamConfigItems() {
            return TransmissionConfigItemConvert.INSTANCE.convert2DTOList(
                configItemMapper.selectList(TransmissionConfigItemPageReqVO.builder()
                    .nodeTypes(context.getLogicConfig().getDependency().getDownstreamNodes())
                    .configPackageId(context.getServicePack().getConfigPackage().getId())
                    .build()));
        }

        /**
         * 判断是否为协议配置 检查配置项类型是否为协议配置
         *
         * @param configItem 配置项
         * @return 是否为协议配置
         */
        private boolean isProtocolConfig(TransmissionConfigItemDTO configItem) {
            return DslTypeEnum.PROTOCOL.getCode().equals(configItem.getDslType());
        }

        /**
         * 创建任务实体 根据配置项创建标准的任务实体，设置默认值
         * <p>
         * 设置内容： 1. 基础信息：租户、服务包、机构等 2. 业务信息：节点类型、API编码等 3. 任务属性：状态、重试配置、优先级等
         *
         * @param configItem 配置项信息
         * @return 任务实体
         */
        private TransmissionTaskRecordSaveReqVO createTask(TransmissionConfigItemDTO configItem) {
            return TransmissionConvert.INSTANCE.createTask(configItem, context);
        }

        /**
         * 添加下游请求 将下游任务转换为请求对象并添加到上下文中
         * <p>
         * 处理流程： 1. 构建下游请求DTO 2. 添加到请求列表
         *
         * @param downstreamTask 下游任务实体
         */
        void addDownstreamRequest(TransmissionTaskRecordSaveReqVO downstreamTask) {
            context.getReqDTO().downstreamReqListGet().add(
                TransmissionConvert.INSTANCE.buildDownstreamReqDTO(downstreamTask, context));
        }
    }

    /**
     * 获取处理器支持的节点类型
     */
    public NodeTypeEnum getNodeType() {
        // Implementation needed
        throw exception(TRANSMISSION_NOT_SUPPORT_NODE_TYPE);
    }

    /**
     * 获取处理器支持的机构类型
     */
    public OrganTypeEnum getOrganType() {
        // Implementation needed
        throw exception(TRANSMISSION_NOT_SUPPORT_ORGAN_TYPE);
    }
}