<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.product.server.dal.mysql.catalog.MedicalCatalogDetailMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->

  <resultMap id="BaseResultMap" type="com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.MedicalCatalogDetailDO">
    <id column="id" property="id"/>
    <result property="catalogId" column="catalog_id"/>
    <result property="projectCode" column="project_code"/>
    <result property="projectName" column="project_name"/>
    <result property="brandName" column="brand_name"/>
    <result property="mnemonicCode" column="mnemonic_code"/>
    <result property="projectType" column="project_type"/>
    <result property="projectLevel" column="project_level"/>
    <result property="regSpec" column="reg_spec"/>
    <result property="spec" column="spec"/>
    <result property="regDosageForm" column="reg_dosage_form"/>
    <result property="dosageForm" column="dosage_form"/>
    <result property="minPackageNum" column="min_package_num"/>
    <result property="minPreparationUnit" column="min_preparation_unit"/>
    <result property="minPackageUnit" column="min_package_unit"/>
    <result property="pharmaceuticalEnterprise" column="pharmaceutical_enterprise"/>
    <result property="manufacturer" column="manufacturer"/>
    <result property="approvalNumber" column="approval_number"/>
    <result property="standardCode" column="standard_code"/>
    <result property="disable" column="disable"/>
    <result property="remark" column="remark"/>
    <result property="ext" column="ext" typeHandler="com.xyy.saas.inquiry.annotation.JsonTypeHandler"/>
    <result property="createTime" column="create_time"/>
    <result property="updateTime" column="update_time"/>
    <result property="deleted" column="deleted"/>
    <result property="creator" column="creator"/>
    <result property="updater" column="updater"/>
  </resultMap>

  <sql id="Base_Column_List">
    id, catalog_id, project_code, project_name, brand_name, mnemonic_code, project_type, project_level,
    reg_spec, spec, reg_dosage_form, dosage_form, min_package_num, min_preparation_unit, min_package_unit,
    pharmaceutical_enterprise, manufacturer, approval_number, standard_code, disable, remark, ext,
    create_time, update_time, deleted, creator, updater
  </sql>

  <sql id="Select_Fulltext_Score_dtl">
    <if test="param.mixedNameQuery != null">
      , MATCH (dtl.project_name, dtl.brand_name, dtl.mnemonic_code) AGAINST (#{param.mixedNameQuery} IN BOOLEAN MODE) AS score
    </if>
    <if test="param.natureNameQuery != null">
      , MATCH (dtl.project_name, dtl.brand_name, dtl.mnemonic_code) AGAINST (#{param.natureNameQuery}) AS score
    </if>
  </sql>

  <sql id="Order_By_Length_And_Score_dtl">
    ORDER BY length(dtl.project_name)
    <if test="param.mixedNameQuery != null or param.natureNameQuery != null">
      , score DESC
    </if>
  </sql>

  <sql id="Where_Search_Detail">
    <if test="param.mixedNameQuery != null">
      AND MATCH (dtl.project_name, dtl.brand_name, dtl.mnemonic_code) AGAINST (#{param.mixedNameQuery} IN BOOLEAN MODE)
    </if>
    <if test="param.natureNameQuery != null">
      AND MATCH (dtl.project_name, dtl.brand_name, dtl.mnemonic_code) AGAINST (#{param.natureNameQuery})
    </if>
    <if test="param.projectTypeList != null and param.projectTypeList.size() > 0">
      AND dtl.project_type in
      <foreach collection="param.projectTypeList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="param.commonNameList != null and param.commonNameList.size() > 0">
      AND dtl.project_name in
      <foreach collection="param.commonNameList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="param.spec != null and param.spec != ''">
      AND dtl.spec = #{param.spec}
    </if>
    <if test="param.specLike != null and param.specLike != ''">
      AND dtl.spec like concat('%', #{param.specLike}, '%')
    </if>
    <if test="param.manufacturer != null">
      AND dtl.manufacturer = #{param.manufacturer}
    </if>
    <if test="param.manufacturerLike != null">
      AND dtl.manufacturer like concat('%', #{param.manufacturerLike}, '%')
    </if>
    <if test="param.approvalNumber != null">
      AND dtl.approval_number = #{param.approvalNumber}
    </if>
    <if test="param.approvalNumberList != null and param.approvalNumberList.size() > 0">
      AND dtl.approval_number IN
      <foreach collection="param.approvalNumberList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="param.disable != null">
      AND dtl.disable = #{param.disable}
    </if>
  </sql>

  <!-- 批量插入 -->
  <insert id="batchInsertByXml" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO saas_medical_catalog_detail
    (
    catalog_id, project_code, project_name, brand_name, mnemonic_code, project_type, project_level,
    reg_spec, spec, reg_dosage_form, dosage_form, min_package_num, min_preparation_unit, min_package_unit,
    pharmaceutical_enterprise, manufacturer, approval_number, standard_code, disable, remark, ext
    )
    VALUES
    <foreach collection="list" index="index" item="item" open="" close="" separator=",">
      (
      #{item.catalogId,jdbcType=BIGINT},
      #{item.projectCode,jdbcType=VARCHAR},
      #{item.projectName,jdbcType=VARCHAR},
      #{item.brandName,jdbcType=VARCHAR},
      #{item.mnemonicCode,jdbcType=VARCHAR},
      #{item.projectType,jdbcType=VARCHAR},
      #{item.projectLevel,jdbcType=VARCHAR},
      #{item.regSpec,jdbcType=VARCHAR},
      #{item.spec,jdbcType=VARCHAR},
      #{item.regDosageForm,jdbcType=VARCHAR},
      #{item.dosageForm,jdbcType=VARCHAR},
      #{item.minPackageNum,jdbcType=VARCHAR},
      #{item.minPreparationUnit,jdbcType=VARCHAR},
      #{item.minPackageUnit,jdbcType=VARCHAR},
      #{item.pharmaceuticalEnterprise,jdbcType=VARCHAR},
      #{item.manufacturer,jdbcType=VARCHAR},
      #{item.approvalNumber,jdbcType=VARCHAR},
      #{item.standardCode,jdbcType=VARCHAR},
      #{item.disable,jdbcType=BIT},
      #{item.remark,jdbcType=VARCHAR},
      #{item.ext,jdbcType=VARCHAR, typeHandler=com.xyy.saas.inquiry.annotation.JsonTypeHandler}
      )
    </foreach>
  </insert>

  <select id="getProjectCodeSetByCatalogId" resultType="java.lang.String">
    SELECT project_code
    FROM saas_medical_catalog_detail
    WHERE catalog_id = #{catalogId}
  </select>

  <!-- 医保目录商品名称推荐搜索 -->
  <select id="selectSuggestNamesByProductsName" resultType="java.lang.String">
    SELECT dtl.project_name
    <include refid="Select_Fulltext_Score_dtl"/>
    FROM saas_medical_catalog_detail dtl
    WHERE dtl.catalog_id = #{param.catalogId}
      AND dtl.deleted = 0
      AND dtl.disable = 0
      <include refid="Where_Search_Detail" />
    GROUP BY dtl.project_name
    <include refid="Order_By_Length_And_Score_dtl" />
    LIMIT
    <if test="offset != null">
      #{offset},
    </if>
    #{limit}
  </select>

  <!-- 医保目录商品搜索（支持规格过滤） -->
  <select id="selectProductsByNameSpec" resultMap="BaseResultMap">
    SELECT dtl.*
    <include refid="Select_Fulltext_Score_dtl"/>
    FROM saas_medical_catalog_detail dtl
    WHERE dtl.catalog_id = #{param.catalogId}
      AND dtl.deleted = 0
      AND dtl.disable = 0
      <include refid="Where_Search_Detail" />
    <include refid="Order_By_Length_And_Score_dtl" />
    LIMIT
    <if test="offset != null">
      #{offset},
    </if>
    #{limit}
  </select>

</mapper>
