<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.product.server.dal.mysql.catalog.CatalogMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

  <resultMap id="BaseResultMap" type="com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.CatalogDO">
    <id column="id" property="id"/>
    <result property="pref" column="pref"/>
    <result property="name" column="name"/>
    <result property="type" column="type"/>
    <result property="projectCodeType" column="project_code_type"/>
    <result property="version" column="version"/>
    <result property="versionCode" column="version_code"/>
    <result property="province" column="province"/>
    <result property="provinceCode" column="province_code"/>
    <result property="city" column="city"/>
    <result property="cityCode" column="city_code"/>
    <result property="area" column="area"/>
    <result property="areaCode" column="area_code"/>
    <result property="uploadUrl" column="upload_url"/>
    <result property="needDownload" column="need_download"/>
    <result property="downloadUrl" column="download_url"/>
    <result property="totalCount" column="total_count"/>
    <result property="matchedCount" column="matched_count"/>
    <result property="unmatchedCount" column="unmatched_count"/>
    <result property="disable" column="disable"/>
    <result property="env" column="env"/>
    <result property="remark" column="remark"/>
    <result property="createTime" column="create_time"/>
    <result property="updateTime" column="update_time"/>
    <result property="deleted" column="deleted"/>
    <result property="creator" column="creator"/>
    <result property="updater" column="updater"/>
  </resultMap>

  <select id="getCatalogPage" resultMap="BaseResultMap">
    select
      *
    from
      saas_catalog
    where
      deleted = 0
      and id in (
        select max(id) from saas_catalog group by type, pref
      )
    <if test="pageReqVO.type != null">
      and type = #{pageReqVO.type}
    </if>
    <if test="pageReqVO.name != null and pageReqVO.name != ''">
      and name like concat('%',#{pageReqVO.name},'%')
    </if>
    <if test="pageReqVO.provinceCode != null and pageReqVO.provinceCode != ''">
      and province_code = #{pageReqVO.provinceCode}
    </if>
    <if test="pageReqVO.cityCode != null and pageReqVO.cityCode != ''">
      and city_code = #{pageReqVO.cityCode}
    </if>
    <if test="pageReqVO.areaCode != null and pageReqVO.areaCode != ''">
      and area_code = #{pageReqVO.areaCode}
    </if>
    order by id desc
  </select>

  <update id="accumulationCatalogTotalCount">
    update saas_catalog
    set total_count = total_count + #{count} ,
        matched_count = matched_count + #{count} ,
        update_time = now()
    where id = #{catalogId}
  </update>

</mapper>