<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductStdlibMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->
  <!-- 商品标准库关联映射 -->
  <resultMap id="BaseResultMap" type="com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO">
    <result column="id" property="id"/>
    <result column="mid_stdlib_id" property="midStdlibId"/>
    <result column="mid_stdlib_id_bak" property="midStdlibIdBak"/>
    <result column="common_name" property="commonName"/>
    <result column="brand_name" property="brandName"/>
    <result column="spec" property="spec"/>
    <result column="barcode" property="barcode"/>
    <result column="manufacturer" property="manufacturer"/>
    <result column="approval_number" property="approvalNumber"/>
    <result column="key_point_hash" property="keyPointHash"/>
    <result column="mnemonic_code" property="mnemonicCode"/>
    <result column="min_package_num" property="minPackageNum"/>
    <result column="images" property="images" typeHandler="cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler"/>
    <result column="spu_category" property="spuCategory"/>
    <result column="unit" property="unit"/>
    <result column="dosage_form" property="dosageForm"/>
    <result column="business_scope" property="businessScope"/>
    <result column="pres_category" property="presCategory"/>
    <result column="storage_way" property="storageWay"/>
    <result column="origin" property="origin"/>
    <result column="manufacturer_uscc" property="manufacturerUscc"/>
    <result column="product_validity" property="productValidity"/>
    <result column="approval_validity_period" property="approvalValidityPeriod"/>
    <result column="marketing_authority_holder" property="marketingAuthorityHolder"/>
    <result column="drug_ident_code" property="drugIdentCode"/>
    <result column="usage_method" property="usageMethod"/>
    <result column="usage_frequency" property="usageFrequency"/>
    <result column="single_dosage" property="singleDosage"/>
    <result column="single_dosage_unit" property="singleDosageUnit"/>
    <result column="first_category" property="firstCategory"/>
    <result column="second_category" property="secondCategory"/>
    <result column="third_category" property="thirdCategory"/>
    <result column="fourth_category" property="fourthCategory"/>
    <result column="five_category" property="fiveCategory"/>
    <result column="six_category" property="sixCategory"/>
    <result column="ext" property="ext" typeHandler="com.xyy.saas.inquiry.annotation.JsonTypeHandler"/>
    <result column="multi_flag" property="multiFlag"/>
    <result column="status" property="status"/>
    <result column="remark" property="remark"/>
    <result column="creator" property="creator"/>
    <result column="create_time" property="createTime"/>
    <result column="updater" property="updater"/>
    <result column="update_time" property="updateTime"/>
    <result column="deleted" property="deleted"/>
  </resultMap>

  <sql id="Select_Columns_Stdlib">
    id
    ,
    mid_stdlib_id,
    mid_stdlib_id_bak,
    common_name,
    brand_name,
    spec,
    barcode,
    manufacturer,
    approval_number,
    key_point_hash,
    mnemonic_code,
    min_package_num,
    images,
    spu_category,
    unit,
    dosage_form,
    business_scope,
    pres_category,
    storage_way,
    origin,
    manufacturer_uscc,
    product_validity,
    approval_validity_period,
    marketing_authority_holder,
    drug_ident_code,
    usage_method,
    usage_frequency,
    single_dosage,
    single_dosage_unit,
    first_category,
    second_category,
    third_category,
    fourth_category,
    five_category,
    six_category,
    ext,
    multi_flag,
    status,
    remark,
    creator,
    create_time,
    updater,
    update_time,
    deleted
  </sql>

  <sql id="Select_Fulltext_Score">
    <if test="param.mixedQuery != null">
      , MATCH (stdlib.common_name, stdlib.brand_name, stdlib.barcode, stdlib.mnemonic_code, stdlib.approval_number, stdlib.manufacturer) AGAINST (#{param.mixedQuery} IN BOOLEAN MODE) AS score
    </if>
    <if test="param.natureQuery != null">
      , MATCH (stdlib.common_name, stdlib.brand_name, stdlib.barcode, stdlib.mnemonic_code, stdlib.approval_number, stdlib.manufacturer) AGAINST (#{param.natureQuery}) AS score
    </if>
    <if test="param.mixedNameQuery != null">
      , MATCH (stdlib.common_name, stdlib.brand_name, stdlib.mnemonic_code) AGAINST (#{param.mixedNameQuery} IN BOOLEAN MODE) AS score
    </if>
    <if test="param.natureNameQuery != null">
      , MATCH (stdlib.common_name, stdlib.brand_name, stdlib.mnemonic_code) AGAINST (#{param.natureNameQuery}) AS score
    </if>
  </sql>

  <sql id="Order_By_Length_And_Score">
    <if test="param.mixedQuery != null or param.natureQuery != null or param.mixedNameQuery != null or param.natureNameQuery != null">
      ORDER BY length(stdlib.common_name), score DESC
    </if>
  </sql>

  <sql id="Where_Search_Stdlib">
    <if test="param.internetRegulatoryCatalogId != null">
      AND cd.deleted = 0 AND cd.disable = 0
    </if>
    <if test="param.isMidStdlib != null">
      <choose>
        <when test="param.isMidStdlib">
          AND stdlib.mid_stdlib_id IS NOT NULL
        </when>
        <otherwise>
          AND stdlib.mid_stdlib_id IS NULL
        </otherwise>
      </choose>
    </if>
    <if test="param.midStdlibIdList != null and param.midStdlibIdList.size() > 0">
      AND stdlib.mid_stdlib_id IN
      <foreach collection="param.midStdlibIdList" item="midStdlibId" open="(" close=")" separator=",">
        #{midStdlibId}
      </foreach>
    </if>
    <if test="param.idList != null and param.idList.size() > 0">
      AND stdlib.id IN
      <foreach collection="param.idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
    <if test="param.mixedQuery != null">
      AND MATCH (stdlib.common_name, stdlib.brand_name, stdlib.barcode, stdlib.mnemonic_code, stdlib.approval_number, stdlib.manufacturer) AGAINST (#{param.mixedQuery} IN BOOLEAN MODE)
    </if>
    <if test="param.natureQuery != null">
      AND MATCH (stdlib.common_name, stdlib.brand_name, stdlib.barcode, stdlib.mnemonic_code, stdlib.approval_number, stdlib.manufacturer) AGAINST (#{param.natureQuery})
    </if>
    <if test="param.mixedNameQuery != null">
      AND MATCH (stdlib.common_name, stdlib.brand_name, stdlib.mnemonic_code) AGAINST (#{param.mixedNameQuery} IN BOOLEAN MODE)
    </if>
    <if test="param.natureNameQuery != null">
      AND MATCH (stdlib.common_name, stdlib.brand_name, stdlib.mnemonic_code) AGAINST (#{param.natureNameQuery})
    </if>
    <if test="param.spuCategory != null">
      AND stdlib.spu_category = #{param.spuCategory}
    </if>
    <if test="param.spec != null and param.spec != ''">
      AND stdlib.spec = #{param.spec}
    </if>
    <if test="param.specLike != null and param.specLike != ''">
      AND stdlib.spec like concat('%', #{param.specLike}, '%')
    </if>
    <if test="param.presCategory != null">
      AND stdlib.pres_category = #{param.presCategory}
    </if>
    <if test="param.barcode != null">
      AND stdlib.barcode = #{param.barcode}
    </if>
    <if test="param.barcodeList != null and param.barcodeList.size() > 0">
      <foreach collection="param.barcodeList" item="item" open="and stdlib.barcode in (" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="param.manufacturer != null">
      AND stdlib.manufacturer = #{param.manufacturer}
    </if>
    <if test="param.approvalNumber != null">
      AND stdlib.approval_number = #{param.approvalNumber}
    </if>
    <if test="param.approvalNumberList != null and param.approvalNumberList.size() > 0">
      AND stdlib.approval_number IN
      <foreach collection="param.approvalNumberList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="param.commonNameList != null and param.commonNameList.size() > 0">
      AND stdlib.common_name in
      <foreach collection="param.commonNameList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="param.firstCategory != null">
      AND stdlib.first_category = #{param.firstCategory}
    </if>
    <if test="param.secondCategory != null">
      AND stdlib.second_category = #{param.secondCategory}
    </if>
    <if test="param.thirdCategory != null">
      AND stdlib.third_category = #{param.thirdCategory}
    </if>
    <if test="param.fourthCategory != null">
      AND stdlib.fourth_category = #{param.fourthCategory}
    </if>
    <if test="param.fiveCategory != null">
      AND stdlib.five_category = #{param.fiveCategory}
    </if>
    <if test="param.sixCategory != null">
      AND stdlib.six_category = #{param.sixCategory}
    </if>
    <if test="param.status != null">
      AND stdlib.status = #{param.status}
    </if>
    <if test="param.disable != null">
      AND stdlib.disable = #{param.disable}
    </if>
    <if test="param.multiFlag != null">
      AND ${@com.xyy.saas.inquiry.product.api.product.dto.ProductFlag@toStdlibFlagSql(param.multiFlag, 'stdlib.multi_flag')}
    </if>
  </sql>

  <update id="batchUpdateStdlib">
    UPDATE saas_product_stdlib
    <set>
      <if test="param.disable != null">
        disable = #{param.disable},
      </if>
      <if test="param.productFlag != null">
        ${@com.xyy.saas.inquiry.product.api.product.dto.ProductFlag@toFlagUpdateSql(param.productFlag, 'multi_flag', true)},
      </if>
    </set>
    WHERE id IN
    <foreach collection="param.idList" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </update>

  <select id="selectDistinctCommonName" resultMap="BaseResultMap">
    SELECT
      stdlib.id,
      stdlib.mid_stdlib_id,
      stdlib.mid_stdlib_id_bak,
      stdlib.common_name,
      stdlib.brand_name,
      stdlib.spec,
      stdlib.barcode,
      stdlib.manufacturer,
      stdlib.approval_number,
      stdlib.key_point_hash,
      stdlib.mnemonic_code,
      stdlib.min_package_num,
      stdlib.spu_category,
      stdlib.unit,
      stdlib.dosage_form,
      group_concat(distinct stdlib.business_scope) as business_scope,
      stdlib.pres_category,
      stdlib.storage_way,
      stdlib.origin,
      stdlib.manufacturer_uscc,
      stdlib.product_validity,
      stdlib.approval_validity_period,
      stdlib.marketing_authority_holder,
      stdlib.drug_ident_code,
      stdlib.usage_method,
      stdlib.usage_frequency,
      stdlib.single_dosage,
      stdlib.single_dosage_unit,
      stdlib.first_category,
      stdlib.second_category,
      stdlib.third_category,
      stdlib.fourth_category,
      stdlib.five_category,
      stdlib.six_category,
      stdlib.ext,
      stdlib.multi_flag,
      stdlib.status,
      stdlib.remark,
      stdlib.creator,
      stdlib.create_time,
      stdlib.updater,
      stdlib.update_time,
      stdlib.deleted
    <include refid="Select_Fulltext_Score" />
    FROM saas_product_stdlib stdlib
    <if test="param.internetRegulatoryCatalogId != null">
      JOIN saas_regulatory_catalog_detail cd ON cd.catalog_id = #{param.internetRegulatoryCatalogId} and stdlib.mid_stdlib_id = cd.project_code
    </if>
    WHERE stdlib.deleted = 0
    <include refid="Where_Search_Stdlib" />
    group by stdlib.common_name
    <include refid="Order_By_Length_And_Score" />
    LIMIT
    <if test="offset != null">
      #{offset},
    </if>
    #{limit}
  </select>


  <select id="searchStdlibProductList" resultMap="BaseResultMap">
    SELECT stdlib.*
    <include refid="Select_Fulltext_Score" />
    FROM saas_product_stdlib stdlib
    <if test="param.internetRegulatoryCatalogId != null">
      JOIN saas_regulatory_catalog_detail cd ON cd.catalog_id = #{param.internetRegulatoryCatalogId} and stdlib.mid_stdlib_id = cd.project_code
    </if>
    WHERE stdlib.deleted = 0
    <include refid="Where_Search_Stdlib"/>
    <include refid="Order_By_Length_And_Score" />
    LIMIT
    <if test="offset != null">
      #{offset},
    </if>
    #{limit}
  </select>


  <select id="searchStdlibProductListGroupByNameSpec" resultMap="BaseResultMap">
    SELECT stdlib.* FROM saas_product_stdlib stdlib
    <if test="param.internetRegulatoryCatalogId != null">
      JOIN saas_regulatory_catalog_detail cd ON cd.catalog_id = #{param.internetRegulatoryCatalogId} and stdlib.mid_stdlib_id = cd.project_code
    </if>
    WHERE stdlib.deleted = 0
    <include refid="Where_Search_Stdlib"/>
    GROUP BY stdlib.common_name, stdlib.spec
    LIMIT #{limit}
  </select>


</mapper>