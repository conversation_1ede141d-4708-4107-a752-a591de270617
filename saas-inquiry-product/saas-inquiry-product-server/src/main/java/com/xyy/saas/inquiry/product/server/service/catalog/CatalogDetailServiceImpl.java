package com.xyy.saas.inquiry.product.server.service.catalog;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.product.enums.CatalogTypeEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.MedicalCatalogDetailExcelVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.MedicalCatalogDetailPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.RegulatoryCatalogDetailExcelVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.RegulatoryCatalogDetailPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.UnifiedCatalogDetailPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.UnifiedCatalogDetailRespVO;
import com.xyy.saas.inquiry.product.server.convert.catalog.MedicalCatalogDetailConvert;
import com.xyy.saas.inquiry.product.server.convert.catalog.RegulatoryCatalogDetailConvert;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.MedicalCatalogDetailDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.RegulatoryCatalogDetailDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.catalog.CatalogMapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PARAM_INVALID;

/**
 * 统一目录明细服务实现类
 * 
 * 策略路由器：根据目录类型自动路由到对应的具体服务实现
 * 负责参数类型转换和返回值转换，不包含具体业务逻辑
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class CatalogDetailServiceImpl implements CatalogDetailService {

    @Resource
    private RegulatoryCatalogDetailService regulatoryCatalogDetailService;

    @Resource
    private MedicalCatalogDetailService medicalCatalogDetailService;

    @Resource
    private CatalogMapper catalogMapper;


    @Override
    public CatalogTypeEnum getCatalogType(Long catalogId) {
        if (catalogId == null) {
            throw exception(PARAM_INVALID, "catalogId", "不能为空");
        }
        Integer catalogType = catalogMapper.selectCatalogTypeById(catalogId);
        if (catalogType == null) {
            return CatalogTypeEnum.REGULATORY; // 默认监管类型，保持向后兼容
        }
        return CatalogTypeEnum.getCatalogTypeDefaultRegulatory(catalogType);
    }

    @Override
    public List<UnifiedCatalogDetailRespVO> listByCatalogId(Long catalogId) {
        CatalogTypeEnum catalogType = getCatalogType(catalogId);
        
        if (catalogType.isMedical()) {
            // 医保目录：调用医保服务并转换为统一响应格式
            List<MedicalCatalogDetailDO> medicalList = medicalCatalogDetailService.getCatalogDetailListByCatalogId(catalogId);
            return convertMedicalListToUnified(medicalList);
        }

        // 监管目录：调用监管服务并转换为统一响应格式
        List<RegulatoryCatalogDetailDO> regulatoryList = regulatoryCatalogDetailService.getRegulatoryCatalogDetailListByCatalogId(catalogId);
        return convertRegulatoryListToUnified(regulatoryList);
    }

    @Override
    public PageResult<UnifiedCatalogDetailRespVO> getCatalogDetailPage(UnifiedCatalogDetailPageReqVO pageReqVO) {
        CatalogTypeEnum catalogType = getCatalogType(pageReqVO.getCatalogId());
        
        if (catalogType.isMedical()) {
            // 医保目录：转换参数并调用医保服务
            MedicalCatalogDetailPageReqVO medicalPageReqVO = pageReqVO.toMedicalPageReqVO();
            PageResult<MedicalCatalogDetailDO> medicalPageResult = medicalCatalogDetailService.getCatalogDetailPage(medicalPageReqVO);
            
            // 转换为统一响应格式
            return convertMedicalPageResultToUnified(medicalPageResult);
        }

        // 监管目录：转换参数并调用监管服务
        RegulatoryCatalogDetailPageReqVO regulatoryPageReqVO = pageReqVO.toRegulatoryPageReqVO();
        PageResult<RegulatoryCatalogDetailDO> pageResult = regulatoryCatalogDetailService.getRegulatoryCatalogDetailPage(regulatoryPageReqVO);

        // 转换为统一响应格式
        return convertRegulatoryPageResultToUnified(pageResult);
    }

    @Override
    public Boolean updateStatus(UnifiedCatalogDetailPageReqVO pageReqVO) {
        CatalogTypeEnum catalogType = getCatalogType(pageReqVO.getCatalogId());
        
        if (catalogType.isMedical()) {
            // 医保目录：转换参数并调用医保服务
            MedicalCatalogDetailPageReqVO medicalPageReqVO = pageReqVO.toMedicalPageReqVO();
            return medicalCatalogDetailService.updateStatus(medicalPageReqVO);
        }

        // 监管目录：转换参数并调用监管服务
        RegulatoryCatalogDetailPageReqVO regulatoryPageReqVO = pageReqVO.toRegulatoryPageReqVO();
        return regulatoryCatalogDetailService.updateStatus(regulatoryPageReqVO);
    }

    @Override
    public CatalogRespVO importExcel(CatalogSaveReqVO catalogSaveReqVO) {
        if (catalogSaveReqVO.getId() == null) {
            throw exception(PARAM_INVALID, "id", "不能为空");
        }
        CatalogTypeEnum catalogType = getCatalogType(catalogSaveReqVO.getId());
        
        if (catalogType.isMedical()) {
            // 医保目录：调用医保服务的导入方法
            return medicalCatalogDetailService.importExcel(catalogSaveReqVO);
        }

        // 监管目录：保持现有逻辑不变
        return regulatoryCatalogDetailService.importExcel(catalogSaveReqVO);
    }

    /**
     * 下载导入模板
     *
     * @param catalogType
     * @param response
     * @throws IOException
     */
    @Override
    public void downloadImportTemplate(Integer catalogType, HttpServletResponse response) throws IOException {
        CatalogTypeEnum catalogTypeEnum = CatalogTypeEnum.getCatalogTypeDefaultRegulatory(catalogType);

        if (catalogTypeEnum.isMedical()) {
            // 输出Excel模板
            ExcelUtils.write(response, "医保目录明细.xls", "数据", MedicalCatalogDetailExcelVO.class, List.of());
            return;
        }

        // 监管目录：保持现有逻辑不变
        ExcelUtils.write(response, "监管目录明细.xls", "数据", RegulatoryCatalogDetailExcelVO.class, List.of());
    }

    /**
     * 下载导出Excel
     *
     * @param pageReqVO
     * @param response
     * @throws IOException
     */
    @Override
    public void downloadExportExcel(UnifiedCatalogDetailPageReqVO pageReqVO, HttpServletResponse response) throws IOException {
        CatalogTypeEnum catalogTypeEnum = getCatalogType(pageReqVO.getCatalogId());

        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageReqVO.setDisable(false);

        if (catalogTypeEnum.isMedical()) {
            // 医保目录：转换参数并调用医保服务
            MedicalCatalogDetailPageReqVO medicalPageReqVO = pageReqVO.toMedicalPageReqVO();
            PageResult<MedicalCatalogDetailDO> pageResult = medicalCatalogDetailService.getCatalogDetailPage(medicalPageReqVO);

            ExcelUtils.write(response, "医保目录明细.xls", "数据", MedicalCatalogDetailExcelVO.class,
                MedicalCatalogDetailConvert.INSTANCE.convertToExcelList(pageResult.getList()));
            return;
        }

        // 监管目录：转换参数并调用监管服务
        RegulatoryCatalogDetailPageReqVO regulatoryPageReqVO = pageReqVO.toRegulatoryPageReqVO();
        PageResult<RegulatoryCatalogDetailDO> pageResult = regulatoryCatalogDetailService.getRegulatoryCatalogDetailPage(regulatoryPageReqVO);

        ExcelUtils.write(response, "监管目录明细.xls", "数据", RegulatoryCatalogDetailExcelVO.class,
            RegulatoryCatalogDetailConvert.INSTANCE.convertDOList2ExcelVOList(pageResult.getList()));
    }

    /**
     * 将医保目录列表转换为统一响应格式
     * 
     * @param medicalList 医保目录列表
     * @return 统一响应格式列表
     */
    private List<UnifiedCatalogDetailRespVO> convertMedicalListToUnified(List<MedicalCatalogDetailDO> medicalList) {
        if (medicalList == null || medicalList.isEmpty()) {
            return Lists.newArrayList();
        }
        
        return MedicalCatalogDetailConvert.INSTANCE.convertList(medicalList).stream()
            .map(UnifiedCatalogDetailRespVO::fromMedicalRespVO)
            .toList();
    }

    /**
     * 将医保目录分页结果转换为统一响应格式
     * 
     * @param medicalPageResult 医保目录分页结果
     * @return 统一响应格式列表
     */
    private PageResult<UnifiedCatalogDetailRespVO> convertMedicalPageResultToUnified(PageResult<MedicalCatalogDetailDO> medicalPageResult) {
        if (medicalPageResult == null || medicalPageResult.getList() == null) {
            return PageResult.empty();
        }
        List<UnifiedCatalogDetailRespVO> unifiedList = convertMedicalListToUnified(medicalPageResult.getList());
        return new PageResult<>(unifiedList, medicalPageResult.getTotal());
    }

    /**
     * 将监管目录列表转换为统一响应格式
     *
     * @param detailDOList 监管目录列表
     * @return 统一响应格式列表
     */
    private List<UnifiedCatalogDetailRespVO> convertRegulatoryListToUnified(List<RegulatoryCatalogDetailDO> detailDOList) {
        if (detailDOList == null || detailDOList.isEmpty()) {
            return Lists.newArrayList();
        }

        return RegulatoryCatalogDetailConvert.INSTANCE.convertList(detailDOList).stream()
            .map(UnifiedCatalogDetailRespVO::fromRegulatoryRespVO)
            .toList();
    }

    /**
     * 将监管目录分页结果转换为统一响应格式
     *
     * @param pageResult 监管目录分页结果
     * @return 统一响应格式列表
     */
    private PageResult<UnifiedCatalogDetailRespVO> convertRegulatoryPageResultToUnified(PageResult<RegulatoryCatalogDetailDO> pageResult) {
        if (pageResult == null || pageResult.getList() == null) {
            return PageResult.empty();
        }
        List<UnifiedCatalogDetailRespVO> unifiedList = convertRegulatoryListToUnified(pageResult.getList());
        return new PageResult<>(unifiedList, pageResult.getTotal());
    }
}
