package com.xyy.saas.inquiry.product.server.config.forward.dto;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductImgMediaType;
import com.xyy.saas.inquiry.product.enums.ProductSpuCategoryEnum;
import com.xyy.saas.inquiry.product.server.service.product.mid.MeProductTransformUtil;
import jakarta.annotation.Nonnull;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MidGeneralProductPresentNewVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 唯一标识
     */
    private String traceId;

    /**
     * 外部商品编码
     */
    private String outsideCode;

    private String outProductCode;

    /**
     * 来源 1:荷叶健康 2:saas智鹿
     */
    private Byte source;

    /**
     * 上报人ID
     */
    private String createUserId;
    /**
     * 上报人
     */
    private String createUser;
    /**
     * 上报人机构ID
     */
    private String createInstitutionId;
    /**
     * 上报人机构名称
     */
    private String createInstitutionName;

    /**
     * 商品分类(1:普通药品, 2:中药饮片, 3:医疗器械, 4:非药品, 5:赠品)
     * @convert
     */
    private Integer spuCategory;
    /**
     * 通用名
     */
    private String generalName;
    /**
     * 通用名助记码
     */
    private String generalNamecode;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品名称助记码
     */
    private String productNameCode;
    /**
     * 所属经营范围
     */
    private String businessScopeMulti;
    /**
     * 中包装条码
     */
    private String mediumPackageCode;
    /**
     * 件包装条码
     */
    private String piecePackageCode;

    /**
     * 上市许可人
     */
    private String marketAuthor;

    /**
     * 是否监管
     */
    private Byte whetherSupervision;

    /**
     * 存储属性
     */
    private Byte shadingAttr;
    /**
     * 进项税率
     */
    private String inRate;
    /**
     * 出项税率
     */
    private String outRate;

    /**
     * 规格/型号
     */
    private String spec;

    /**
     * 批准文号/注册证号
     */
    private String approvalNo;

    /**
     * 生产厂家
     * @convert
     */
    private String manufacturerName;

    /**
     * 小包装条码
     */
    private String smallPackageCode;

    /**
     * 产地
     */
    private String originPlace;

    /**
     * 剂型文本
     * @convert
     */
    private String dosageFormName;
    /**
     * 存储条件
     * @convert
     */
    private Integer storageCond;

    /**
     * 药品本位码
     */
    private String standardCodes;

    /**
     * 包装单位文本
     * @convert
     */
    private String packageUnitName;

    /**
     * 处方分类
     * @convert
     */
    private Integer prescriptionCategory;


    /**
     * 品牌/商标
     */
    private String brand;

    /**
     * 有效期
     */
    private Short validity;

    /**
     * 质量标准
     */
    private String qualityStandard;

    /**
     * 是否需要精修（1 是 0 否）
     */
    private Integer needTruing;

    /**
     * 是否委托生产(0:否, 1:是)
     */
    private Byte delegationProduct;

    /**
     * 受委托生产厂家名称
     */
    private String entrustedManufacturerName;

    private String manufacturingLicenseNo;

    private Boolean needQueryEs;

    private String productionAddress;

    private String marketAuthorAddress;

    private String filingsAuthor;

    /**
     * 数据是否变更
     */
    private Boolean dataChange;

    /** 商品图片-待精修图片 */
    private List<MidGeneralProductCorrectMediaVo> productImgList;
    /** 外包装图片 */
    private List<MidGeneralProductCorrectMediaVo> outPackageImgList;
    /** 说明书图片 */
    private List<MidGeneralProductCorrectMediaVo> directionImgList;


    public static MidGeneralProductPresentNewVo of(ProductInfoDto dto) {
        MidGeneralProductPresentNewVo vo = new MidGeneralProductPresentNewVo();
        //设置唯一标识
        vo.setTraceId(MeProductTransformUtil.callTraceId());
        //外部商品标识
        vo.setOutsideCode(dto.getPref());
        //上报来源 2-SAAS智鹿
        vo.setSource(MeProductTransformUtil.getSource());

        // 商品大类必传（默认-普通药品）
        ProductSpuCategoryEnum spuCategoryEnum = Optional.ofNullable(ProductSpuCategoryEnum.getByDesc(dto.getSpuCategory())).orElse(ProductSpuCategoryEnum.GENERAL_DRUGS);
        vo.setSpuCategory(spuCategoryEnum.code);

        vo.setGeneralName(dto.getCommonName());
        // vo.setProductName(dto.getBrandName());
        vo.setBrand(dto.getBrandName());
        vo.setSmallPackageCode(dto.getBarcode());
        // vo.setMediumPackageCode(dto.getMiddleUnitBarcode());
        // vo.setWhetherSupervision(null == dto.getWhetherSupervision()? null:dto.getWhetherSupervision().byteValue());
        vo.setInRate(Optional.ofNullable(dto.getInputTaxRate()).map(BigDecimal::toPlainString).orElse(null));
        vo.setOutRate(Optional.ofNullable(dto.getOutputTaxRate()).map(BigDecimal::toPlainString).orElse(null));
        // vo.setShadingAttr(null == dto.getShadingAttr() ? null:dto.getShadingAttr().byteValue());
        // vo.setStorageCond(dto.getStorageWay());
        //产地
        vo.setOriginPlace(dto.getOrigin());
        vo.setSpec(dto.getSpec());
        vo.setApprovalNo(dto.getApprovalNumber());
        vo.setManufacturerName(dto.getManufacturer());
        vo.setMarketAuthor(dto.getManufacturer());
        vo.setSmallPackageCode(dto.getBarcode());

//        Map<String, String> unitMap = dictDataApi.getDictDataList(dto.getTenantId(), DictTypeConstants.PRODUCT_UNIT)
//            .stream().collect(Collectors.toMap(DictDataRespDTO::getValue, DictDataRespDTO::getLabel, (v1, v2) -> v2));
//        vo.setPackageUnitName(unitMap.get("" + dto.getUnitId()));
        vo.setPackageUnitName(dto.getUnit());

        // 外包装第一张图为封面图 1->主图、2-5 ->外包装、其他 ->说明书
        vo.setOutPackageImgList(assembleImgList(ProductImgMediaType.OUTER_PACKAGING, 1, dto.getCoverImages(), dto.getOuterPackageImages()));
        vo.setDirectionImgList(assembleImgList(ProductImgMediaType.INSTRUCTIONS, 6, dto.getInstructionImages()));
        return vo;
    }

    @SafeVarargs
    private static List<MidGeneralProductCorrectMediaVo> assembleImgList(@Nonnull ProductImgMediaType mediaType, int pictureOrdinalStart, List<String>... imgLists) {
        if (imgLists == null || imgLists.length == 0) {
            return null;
        }
        List<MidGeneralProductCorrectMediaVo> result = new ArrayList<>();
        for (List<String> imgList : imgLists) {
            if (CollectionUtils.isEmpty(imgList)) {
                continue;
            }
            for (String img : imgList) {
                result.add(
                    MidGeneralProductCorrectMediaVo.builder()
                        .mediaUrl(img)
                        .mediaType(mediaType.code)
                        .pictureOrdinal(pictureOrdinalStart++)
                        .build()
                );
            }
        }
        return result;
    }
}