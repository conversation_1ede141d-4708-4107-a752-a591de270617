package com.xyy.saas.inquiry.product.server.service.catalog;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.enums.CatalogTypeEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.UnifiedCatalogDetailPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.UnifiedCatalogDetailRespVO;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 统一目录明细服务接口
 * 
 * 使用策略模式，根据目录类型自动路由到对应的具体服务实现
 * 支持医保目录和监管目录的统一操作
 * 
 * <AUTHOR>
 */
public interface CatalogDetailService {

    /**
     * 根据目录ID获取目录类型
     *
     * @param catalogId 目录ID
     * @return 目录类型枚举
     */
    CatalogTypeEnum getCatalogType(Long catalogId);

    /**
     * 根据目录ID获取目录明细列表
     * 
     * @param catalogId 目录ID
     * @return 目录明细列表
     */
    List<UnifiedCatalogDetailRespVO> listByCatalogId(Long catalogId);

    /**
     * 获得目录明细分页
     * 
     * @param pageReqVO 分页查询请求
     * @return 目录明细分页结果
     */
    PageResult<UnifiedCatalogDetailRespVO> getCatalogDetailPage(UnifiedCatalogDetailPageReqVO pageReqVO);

    /**
     * 批量修改目录明细状态
     * 
     * @param pageReqVO 包含状态更新信息的请求
     * @return 更新是否成功
     */
    Boolean updateStatus(UnifiedCatalogDetailPageReqVO pageReqVO);

    /**
     * 导入Excel数据
     * 
     * @param catalogSaveReqVO 目录保存请求，包含导入相关信息
     * @return 导入结果
     */
    CatalogRespVO importExcel(CatalogSaveReqVO catalogSaveReqVO);

    /**
     * 下载导入模板
     *
     * @param catalogType
     * @param response
     * @throws IOException
     */
    void downloadImportTemplate(Integer catalogType, HttpServletResponse response) throws IOException;

    /**
     * 下载导出Excel
     * @param pageReqVO
     * @param response
     * @throws IOException
     */
    void downloadExportExcel(UnifiedCatalogDetailPageReqVO pageReqVO, HttpServletResponse response) throws IOException;

}
