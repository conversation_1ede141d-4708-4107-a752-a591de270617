package com.xyy.saas.inquiry.product.server.dal.dataobject.catalog;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.product.api.catalog.dto.MedicalCatalogDetailExtDTO;
import com.xyy.saas.inquiry.product.utils.ProductUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 医保目录明细 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_medical_catalog_detail", autoResultMap = true)
@KeySequence("saas_medical_catalog_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicalCatalogDetailDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    
    /**
     * 目录ID
     */
    private Long catalogId;
    
    /**
     * 医疗目录编码
     */
    private String projectCode;
    
    /**
     * 医疗目录名称
     */
    private String projectName;
    
    /**
     * 品牌名称
     */
    private String brandName;
    
    /**
     * 助记码
     */
    private String mnemonicCode;
    
    /**
     * 医疗目录类别
     */
    private String projectType;
    
    /**
     * 医疗目录等级
     */
    private String projectLevel;
    
    /**
     * 注册规格
     */
    private String regSpec;
    
    /**
     * 规格
     */
    private String spec;
    
    /**
     * 注册剂型
     */
    private String regDosageForm;
    
    /**
     * 剂型
     */
    private String dosageForm;
    
    /**
     * 最小包装数量
     */
    private String minPackageNum;
    
    /**
     * 最小制剂单位
     */
    private String minPreparationUnit;
    
    /**
     * 最小包装单位
     */
    private String minPackageUnit;
    
    /**
     * 药品企业
     */
    private String pharmaceuticalEnterprise;
    
    /**
     * 生产企业
     */
    private String manufacturer;
    
    /**
     * 批准文号
     */
    private String approvalNumber;
    
    /**
     * 本位码
     */
    private String standardCode;

    /**
     * 是否禁用，默认否
     */
    private Boolean disable;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展字段（JSON格式）
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private MedicalCatalogDetailExtDTO ext;


    /**
     * 组装空值和助记码
     * @return
     */
    public MedicalCatalogDetailDO assembleNullAndMnemonicCode() {
        this.setProjectName(StringUtils.defaultIfBlank(this.getProjectName(), ""));
        this.setBrandName(StringUtils.defaultIfBlank(this.getBrandName(), ""));
        this.setMnemonicCode(ProductUtil.getMnemonicCode(this.getProjectName(), this.getBrandName()));
        this.setProjectType(StringUtils.defaultIfBlank(this.getProjectType(), ""));
        this.setProjectLevel(StringUtils.defaultIfBlank(this.getProjectLevel(), ""));
        this.setSpec(StringUtils.defaultIfBlank(this.getSpec(), ""));
        this.setRegSpec(StringUtils.defaultIfBlank(this.getRegSpec(), ""));
        this.setDosageForm(StringUtils.defaultIfBlank(this.getDosageForm(), ""));
        this.setRegDosageForm(StringUtils.defaultIfBlank(this.getRegDosageForm(), ""));
        this.setMinPackageNum(StringUtils.defaultIfBlank(this.getMinPackageNum(), ""));
        this.setMinPreparationUnit(StringUtils.defaultIfBlank(this.getMinPreparationUnit(), ""));
        this.setMinPackageUnit(StringUtils.defaultIfBlank(this.getMinPackageUnit(), ""));
        this.setPharmaceuticalEnterprise(StringUtils.defaultIfBlank(this.getPharmaceuticalEnterprise(), ""));
        this.setManufacturer(StringUtils.defaultIfBlank(this.getManufacturer(), ""));
        this.setApprovalNumber(StringUtils.defaultIfBlank(this.getApprovalNumber(), ""));
        this.setStandardCode(StringUtils.defaultIfBlank(this.getStandardCode(), ""));

        if (this.getDisable() == null) {
            this.setDisable(false);
        }
        return this;
    }
}
