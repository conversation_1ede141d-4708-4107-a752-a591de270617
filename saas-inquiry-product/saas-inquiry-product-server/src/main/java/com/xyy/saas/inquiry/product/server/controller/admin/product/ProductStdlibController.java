package com.xyy.saas.inquiry.product.server.controller.admin.product;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductMixedPageQueryVo;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductPageQueryVo;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductSyncReqVo;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductUpdateReqVo;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductVo;
import com.xyy.saas.inquiry.product.server.service.product.ProductStdlibService;
import com.xyy.saas.inquiry.product.server.service.product.ProductStdlibSyncService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 商品标准库信息")
@RestController
@RequestMapping("/product/stdlib")
@Validated
@Slf4j
public class ProductStdlibController {

    @Resource
    private ProductStdlibService stdlibService;

    @Resource
    private ProductStdlibSyncService stdlibSyncService;

    @GetMapping("/mixed-page")
    @Operation(summary = "查询混合标准库商品信息分页（中台+自建）")
    @PreAuthorize("@ss.hasPermission('saas:product:info:query')")
    public CommonResult<PageResult<StdlibProductVo>> getMixProductInfoPage(@ParameterObject @Valid StdlibProductMixedPageQueryVo pageReqVO) {
        return success(stdlibService.getMixedStdlibProductPage(pageReqVO));
    }

    @GetMapping("/self-page")
    @Operation(summary = "查询自建标准库商品信息分页")
    @PreAuthorize("@ss.hasPermission('saas:product:stdlib:query')")
    public CommonResult<PageResult<ProductStdlibDto>> getSelfProductInfoPage(@ParameterObject @Valid StdlibProductPageQueryVo pageReqVO) {
        return success(stdlibService.getSelfStdlibProductPage(pageReqVO));
    }


    @GetMapping("/get")
    @Operation(summary = "获得标准库商品信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:product:stdlib:query')")
    public CommonResult<ProductStdlibDto> getStdlibProduct(@RequestParam("id") Long id) {
        return success(stdlibService.getStdlibProduct(id));
    }

    @PostMapping("/sync")
    @Operation(summary = "同步标准库商品")
    @PreAuthorize("@ss.hasPermission('saas:product:stdlib:sync')")
    public CommonResult<Boolean> sync(@Valid @RequestBody StdlibProductSyncReqVo reqVo) {
        stdlibSyncService.startSingleSync(reqVo);
        return success(true);
    }

    @PostMapping("/update")
    @Operation(summary = "修改标准库商品（停用状态、中台同步覆盖）")
    @PreAuthorize("@ss.hasPermission('saas:product:stdlib:update')")
    public CommonResult<Boolean> disable(@Valid @RequestBody StdlibProductUpdateReqVo reqVo) {
        stdlibService.batchUpdateStdlib(reqVo);
        return success(true);
    }
}