package com.xyy.saas.inquiry.product.server.convert.catalog;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.api.catalog.dto.MedicalCatalogDetailDTO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.MedicalCatalogDetailRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.MedicalCatalogDetailExcelVO;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductSearchRespVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.MedicalCatalogDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MedicalCatalogDetailConvert {

    MedicalCatalogDetailConvert INSTANCE = Mappers.getMapper(MedicalCatalogDetailConvert.class);

    /**
     * DO 转 RespVO
     */
    MedicalCatalogDetailRespVO convert(MedicalCatalogDetailDO bean);

    /**
     * DO列表 转 RespVO列表
     */
    List<MedicalCatalogDetailRespVO> convertList(List<MedicalCatalogDetailDO> list);

    /**
     * DO 转 DTO
     */
    MedicalCatalogDetailDTO convertDo2Dto(MedicalCatalogDetailDO bean);

    /**
     * DO列表 转 DTO列表
     */
    List<MedicalCatalogDetailDTO> convertDo2DtoList(List<MedicalCatalogDetailDO> list);

    /**
     * DO分页 转 RespVO分页
     */
    PageResult<MedicalCatalogDetailRespVO> convertPage(PageResult<MedicalCatalogDetailDO> page);

    /**
     * ExcelVO 转 DO
     */
    @Mapping(target = "ext.regulatoryCode", source = "regulatoryCode")
    MedicalCatalogDetailDO convert(MedicalCatalogDetailExcelVO bean);

    /**
     * ExcelVO列表 转 DO列表
     */
    List<MedicalCatalogDetailDO> convertExcelList(List<MedicalCatalogDetailExcelVO> list);

    /**
     * DO 转 ExcelVO
     */
    @Mapping(target = "regulatoryCode", source = "ext.regulatoryCode")
    MedicalCatalogDetailExcelVO convertToExcel(MedicalCatalogDetailDO bean);

    /**
     * DO列表 转 ExcelVO列表
     */
    List<MedicalCatalogDetailExcelVO> convertToExcelList(List<MedicalCatalogDetailDO> list);




    /**
     * 将医保目录明细转换为商品搜索响应VO
     *
     * @param medicalDetail 医保目录明细
     * @return 商品搜索响应VO
     */
    @Mapping(target = "pref", source = "projectCode")
    @Mapping(target = "standardId", source = "ext.regulatoryCode")
    @Mapping(target = "commonName", source = "projectName")
    @Mapping(target = "productName", source = "brandName")
    @Mapping(target = "attributeSpecification", source = "spec")
    @Mapping(target = "unitName", source = "minPackageUnit")
    InquiryProductSearchRespVO convertDoToProductSearchRespVO(MedicalCatalogDetailDO medicalDetail);

}
