package com.xyy.saas.inquiry.product.server.controller.admin.catalog;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PARAM_INVALID;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.UnifiedCatalogDetailPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.UnifiedCatalogDetailRespVO;
import com.xyy.saas.inquiry.product.server.service.catalog.CatalogDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 统一目录明细")
@RestController
@RequestMapping("/product/catalog-detail")
@Validated
@Slf4j
public class CatalogDetailController {

    @Resource
    private CatalogDetailService catalogDetailService;


    @GetMapping("/list-by-catalog-id")
    @Operation(summary = "获得目录明细列表")
    @Parameter(name = "catalogId", description = "目录ID")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:query')")
    public CommonResult<List<UnifiedCatalogDetailRespVO>> listByCatalogId(@RequestParam("catalogId") Long catalogId) {
        return success(catalogDetailService.listByCatalogId(catalogId));
    }

    @GetMapping("/page")
    @Operation(summary = "目录明细分页")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:query')")
    public CommonResult<PageResult<UnifiedCatalogDetailRespVO>> getCatalogDetailPage(@Valid UnifiedCatalogDetailPageReqVO pageReqVO) {
        return success(catalogDetailService.getCatalogDetailPage(pageReqVO));
    }

    @PostMapping("/update-status")
    @Operation(summary = "修改状态")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:query')")
    public CommonResult<Boolean> updateStatus(@RequestBody UnifiedCatalogDetailPageReqVO pageReqVO) {
        return success(catalogDetailService.updateStatus(pageReqVO));
    }

    @PostMapping("/importExcel")
    @Operation(summary = "导入")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:query')")
    public CommonResult<CatalogRespVO> importExcel(@RequestBody CatalogSaveReqVO catalogSaveReqVO) {
        return success(catalogDetailService.importExcel(catalogSaveReqVO));
    }

    @GetMapping("/get-import-template")
    @Operation(summary = "获得目录明细导入模板")
    @Parameter(name = "catalogType", description = "目录类型（1:医保 3:互联网监管）")
    public void importTemplate(@RequestParam("catalogType") Integer catalogType, HttpServletResponse response) throws IOException {
        catalogDetailService.downloadImportTemplate(catalogType, response);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出目录 Excel")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCatalogExcel(@Valid UnifiedCatalogDetailPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        catalogDetailService.downloadExportExcel(pageReqVO, response);
    }


}
