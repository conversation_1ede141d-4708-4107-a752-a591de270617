package com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo;

import cn.hutool.core.convert.Convert;
import com.xyy.saas.inquiry.product.api.catalog.dto.MedicalCatalogDetailExtDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 统一目录明细 Response VO")
@Data
@ToString(callSuper = true)
public class UnifiedCatalogDetailRespVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "目录ID")
    private Long catalogId;

    @Schema(description = "是否禁用，默认否")
    private Boolean disable;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    // ==================== 编码相关字段 ====================
    
    @Schema(description = "项目编码（监管目录为Long类型标准库ID，医保目录为String类型编码）")
    private String projectCode;

    @Schema(description = "通用名（监管目录使用）")
    private String commonName;

    @Schema(description = "医疗目录名称/项目名称（医保目录使用）")
    private String projectName;

    @Schema(description = "助记码（医保目录使用）")
    private String mnemonicCode;

    // ==================== 产品信息字段 ====================
    
    @Schema(description = "品牌名称（通用字段）")
    private String brandName;

    @Schema(description = "规格/型号（监管目录使用）")
    private String spec;

    @Schema(description = "注册规格（医保目录使用）")
    private String regSpec;

    @Schema(description = "条形码（监管目录使用）")
    private String barcode;

    @Schema(description = "医疗目录类别（医保目录使用）")
    private String projectType;

    @Schema(description = "医疗目录等级（医保目录使用）")
    private String projectLevel;

    @Schema(description = "注册剂型（医保目录使用）")
    private String regDosageForm;

    @Schema(description = "剂型（医保目录使用）")
    private String dosageForm;

    @Schema(description = "最小包装数量（医保目录使用）")
    private String minPackageNum;

    @Schema(description = "最小制剂单位（医保目录使用）")
    private String minPreparationUnit;

    @Schema(description = "最小包装单位（医保目录使用）")
    private String minPackageUnit;

    // ==================== 企业信息字段 ====================
    
    @Schema(description = "生产厂家/生产企业（通用字段）")
    private String manufacturer;

    @Schema(description = "药品企业（医保目录使用）")
    private String pharmaceuticalEnterprise;

    @Schema(description = "批准文号（通用字段）")
    private String approvalNumber;

    @Schema(description = "本位码（医保目录使用）")
    private String standardCode;

    @Schema(description = "扩展字段（医保目录使用）")
    private MedicalCatalogDetailExtDTO ext;

    // ==================== 类型转换辅助方法 ====================
    
    /**
     * 转换为监管目录响应VO
     */
    public RegulatoryCatalogDetailRespVO toRegulatoryRespVO() {
        RegulatoryCatalogDetailRespVO vo = new RegulatoryCatalogDetailRespVO();
        vo.setId(this.id);
        vo.setCatalogId(this.catalogId);
        vo.setDisable(this.disable);
        vo.setRemark(this.remark);
        
        // 监管目录特有字段
        vo.setProjectCode(Convert.toLong(this.projectCode));
        vo.setCommonName(this.commonName);
        vo.setBrandName(this.brandName);
        vo.setSpec(this.spec);
        vo.setBarcode(this.barcode);
        vo.setManufacturer(this.manufacturer);
        vo.setApprovalNumber(this.approvalNumber);
        
        return vo;
    }

    /**
     * 转换为医保目录响应VO
     */
    public MedicalCatalogDetailRespVO toMedicalRespVO() {
        MedicalCatalogDetailRespVO vo = new MedicalCatalogDetailRespVO();
        vo.setId(this.id);
        vo.setCatalogId(this.catalogId);
        vo.setDisable(this.disable);
        vo.setRemark(this.remark);
        vo.setCreateTime(this.createTime);
        vo.setUpdateTime(this.updateTime);
        
        // 医保目录特有字段
        vo.setProjectCode(this.projectCode);
        vo.setProjectName(this.projectName);
        vo.setBrandName(this.brandName);
        vo.setMnemonicCode(this.mnemonicCode);
        vo.setProjectType(this.projectType);
        vo.setProjectLevel(this.projectLevel);
        vo.setSpec(this.spec);
        vo.setRegSpec(this.regSpec);
        vo.setDosageForm(this.dosageForm);
        vo.setRegDosageForm(this.regDosageForm);
        vo.setMinPackageNum(this.minPackageNum);
        vo.setMinPreparationUnit(this.minPreparationUnit);
        vo.setMinPackageUnit(this.minPackageUnit);
        vo.setPharmaceuticalEnterprise(this.pharmaceuticalEnterprise);
        vo.setManufacturer(this.manufacturer);
        vo.setApprovalNumber(this.approvalNumber);
        vo.setStandardCode(this.standardCode);
        vo.setExt(this.ext);

        return vo;
    }

    /**
     * 从监管目录响应VO创建统一VO
     */
    public static UnifiedCatalogDetailRespVO fromRegulatoryRespVO(RegulatoryCatalogDetailRespVO vo) {
        UnifiedCatalogDetailRespVO unified = new UnifiedCatalogDetailRespVO();
        unified.setId(vo.getId());
        unified.setCatalogId(vo.getCatalogId());
        unified.setDisable(vo.getDisable());
        unified.setRemark(vo.getRemark());
        
        // 监管目录特有字段
        unified.setProjectCode(String.valueOf(vo.getProjectCode()));
        unified.setCommonName(vo.getCommonName());
        unified.setBrandName(vo.getBrandName());
        unified.setSpec(vo.getSpec());
        unified.setBarcode(vo.getBarcode());
        unified.setManufacturer(vo.getManufacturer());
        unified.setApprovalNumber(vo.getApprovalNumber());
        
        return unified;
    }

    /**
     * 从医保目录响应VO创建统一VO
     */
    public static UnifiedCatalogDetailRespVO fromMedicalRespVO(MedicalCatalogDetailRespVO vo) {
        UnifiedCatalogDetailRespVO unified = new UnifiedCatalogDetailRespVO();
        unified.setId(vo.getId());
        unified.setCatalogId(vo.getCatalogId());
        unified.setDisable(vo.getDisable());
        unified.setRemark(vo.getRemark());
        unified.setCreateTime(vo.getCreateTime());
        unified.setUpdateTime(vo.getUpdateTime());
        
        // 医保目录特有字段
        unified.setProjectCode(vo.getProjectCode());
        unified.setProjectName(vo.getProjectName());
        unified.setBrandName(vo.getBrandName());
        unified.setMnemonicCode(vo.getMnemonicCode());
        unified.setProjectType(vo.getProjectType());
        unified.setProjectLevel(vo.getProjectLevel());
        unified.setSpec(vo.getSpec());
        unified.setRegSpec(vo.getRegSpec());
        unified.setDosageForm(vo.getDosageForm());
        unified.setRegDosageForm(vo.getRegDosageForm());
        unified.setMinPackageNum(vo.getMinPackageNum());
        unified.setMinPreparationUnit(vo.getMinPreparationUnit());
        unified.setMinPackageUnit(vo.getMinPackageUnit());
        unified.setPharmaceuticalEnterprise(vo.getPharmaceuticalEnterprise());
        unified.setManufacturer(vo.getManufacturer());
        unified.setApprovalNumber(vo.getApprovalNumber());
        unified.setStandardCode(vo.getStandardCode());
        unified.setExt(vo.getExt());

        return unified;
    }
}
