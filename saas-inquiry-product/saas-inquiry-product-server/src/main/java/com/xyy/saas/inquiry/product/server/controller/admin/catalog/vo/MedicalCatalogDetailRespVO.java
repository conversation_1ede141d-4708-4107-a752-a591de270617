package com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo;

import com.xyy.saas.inquiry.product.api.catalog.dto.MedicalCatalogDetailExtDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 医保目录明细 Response VO")
@Data
@ToString(callSuper = true)
public class MedicalCatalogDetailRespVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "目录ID")
    private Long catalogId;

    @Schema(description = "医疗目录编码")
    private String projectCode;

    @Schema(description = "医疗目录名称")
    private String projectName;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "助记码")
    private String mnemonicCode;

    @Schema(description = "医疗目录类别")
    private String projectType;

    @Schema(description = "医疗目录等级")
    private String projectLevel;

    @Schema(description = "注册规格")
    private String regSpec;

    @Schema(description = "规格")
    private String spec;

    @Schema(description = "注册剂型")
    private String regDosageForm;

    @Schema(description = "剂型")
    private String dosageForm;

    @Schema(description = "最小包装数量")
    private String minPackageNum;

    @Schema(description = "最小制剂单位")
    private String minPreparationUnit;

    @Schema(description = "最小包装单位")
    private String minPackageUnit;

    @Schema(description = "药品企业")
    private String pharmaceuticalEnterprise;

    @Schema(description = "生产企业")
    private String manufacturer;

    @Schema(description = "批准文号")
    private String approvalNumber;

    @Schema(description = "本位码")
    private String standardCode;

    @Schema(description = "扩展字段")
    private MedicalCatalogDetailExtDTO ext;

    @Schema(description = "是否禁用，默认否")
    private Boolean disable;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "更新者")
    private String updater;

}
