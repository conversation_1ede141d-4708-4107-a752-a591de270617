package com.xyy.saas.inquiry.product.server.service.catalog;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.MEDICAL_CATALOG_DETAIL_DATA_IS_NULL;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PARAM_INVALID;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.product.api.catalog.dto.MedicalCatalogProductSearchDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.MedicalCatalogDetailExcelVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.MedicalCatalogDetailPageReqVO;
import com.xyy.saas.inquiry.product.server.convert.catalog.MedicalCatalogDetailConvert;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.MedicalCatalogDetailDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.catalog.CatalogMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.catalog.MedicalCatalogDetailMapper;
import com.xyy.saas.inquiry.product.utils.ProductUtil;
import com.xyy.saas.inquiry.util.excel.EasyExcelUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.validation.ConstraintViolation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.ObjectError;
import org.springframework.validation.Validator;
import org.springframework.validation.annotation.Validated;

/**
 * 医保目录明细 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class MedicalCatalogDetailServiceImpl implements MedicalCatalogDetailService {

    @Resource
    private CatalogMapper catalogMapper;
    @Resource
    private MedicalCatalogDetailMapper medicalCatalogDetailMapper;

    @Resource
    private EasyExcelUtil easyExcelUtil;

    @Resource
    private Validator validator;

    @Resource
    @Lazy
    private MedicalCatalogDetailService selfProxy;


    /**
     * 批量插入
     *
     * @param list
     */
    @Override
    public void batchInsertByXml(List<MedicalCatalogDetailDO> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        Lists.partition(list, 500).forEach(medicalCatalogDetailMapper::batchInsertByXml);
    }

    /**
     * 根据目录ID删除目录明细
     *
     * @param catalogId 目录ID
     */
    @Override
    public void deleteByCatalogId(Long catalogId) {
        if (catalogId == null) {
            throw exception(PARAM_INVALID, "catalogId", "不能为空");
        }
        medicalCatalogDetailMapper.deleteByCatalogId(catalogId);
    }

    @Override
    public boolean updateStatus(MedicalCatalogDetailPageReqVO reqVO) {
        if (reqVO.getId() == null || reqVO.getDisable() == null) {
            throw exception(PARAM_INVALID, "id | disable", "不能为空");
        }

        return medicalCatalogDetailMapper.updateById(MedicalCatalogDetailDO.builder().id(reqVO.getId()).disable(reqVO.getDisable()).build()) > 0;
    }

    @Override
    public MedicalCatalogDetailDO getCatalogDetail(Long id) {
        return medicalCatalogDetailMapper.selectById(id);
    }

    @Override
    public PageResult<MedicalCatalogDetailDO> getCatalogDetailPage(MedicalCatalogDetailPageReqVO pageReqVO) {
        return medicalCatalogDetailMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MedicalCatalogDetailDO> getCatalogDetailListByCatalogId(Long catalogId) {
        return medicalCatalogDetailMapper.selectListByCatalogId(catalogId);
    }

    @Override
    public List<MedicalCatalogDetailDO> getCatalogDetailListByProjectCodes(Long catalogId, List<String> projectCodes) {
        return medicalCatalogDetailMapper.selectListByProjectCodes(catalogId, projectCodes);
    }

    @Override
    public CatalogRespVO analysisCatalogDetailExcel(CatalogSaveReqVO reqVO, List<MedicalCatalogDetailDO> detailList) {
        // 读取excel
        List<MedicalCatalogDetailExcelVO> excelVOList = easyExcelUtil.analysisAndCheckExcel(reqVO.getUploadUrl(), MedicalCatalogDetailExcelVO.class, 1_000_000);
        if (CollUtil.isEmpty(excelVOList)) {
            throw exception(MEDICAL_CATALOG_DETAIL_DATA_IS_NULL);
        }

        // 校验重复
        Set<String> projectCodeSet = reqVO.getId() == null ? new HashSet<>()
            : medicalCatalogDetailMapper.getProjectCodeSetByCatalogId(reqVO.getId());

        Map<Boolean, List<MedicalCatalogDetailExcelVO>> validateResult = excelVOList.stream().map(excelVO -> {
            // 校验重复
            if (projectCodeSet.contains(excelVO.getProjectCode())) {
                excelVO.setErrMsg("医疗目录编码重复,");
            } else {
                projectCodeSet.add(excelVO.getProjectCode());
            }
            // 校验数据
            String errMsg = validator.validateObject(excelVO).getAllErrors().stream().map(ObjectError::getDefaultMessage).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(errMsg)) {
                excelVO.setErrMsg(StringUtils.defaultString(excelVO.getErrMsg()) + errMsg + ",");
            }

            if (StringUtils.isBlank(excelVO.getErrMsg())) {
                MedicalCatalogDetailDO medicalCatalogDetailDO = MedicalCatalogDetailConvert.INSTANCE.convert(excelVO);
                medicalCatalogDetailDO.setCatalogId(reqVO.getId());

                // 组装空值和助记码
                medicalCatalogDetailDO.assembleNullAndMnemonicCode()
                    .setDisable(false);
                // TODO 匹配自建标准库（暂时用sql update）

                detailList.add(medicalCatalogDetailDO);

                return new Pair<>(true, excelVO);
            } else {
                return new Pair<>(false, excelVO);
            }
        }).collect(Collectors.partitioningBy(Pair::getFirst, Collectors.mapping(Pair::getSecond, Collectors.toList())));

        List<MedicalCatalogDetailExcelVO> successList = validateResult.getOrDefault(true, List.of());
        List<MedicalCatalogDetailExcelVO> failureList = validateResult.getOrDefault(false, List.of());

        // 失败数据excel
        String failExcelUrl = "";
        if (CollUtil.isNotEmpty(failureList)) {
            failExcelUrl = easyExcelUtil.writeExcelAndUpload("医保目录明细导入失败记录", MedicalCatalogDetailExcelVO.class, failureList);
        }

        return new CatalogRespVO()
            .setExportSuccessCount(successList.size())
            .setExportFailCount(failureList.size())
            .setFailExcelUrl(failExcelUrl);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CatalogRespVO importExcel(CatalogSaveReqVO pageReqVO) {
        if (pageReqVO.getId() == null || StringUtils.isBlank(pageReqVO.getUploadUrl())) {
            throw exception(PARAM_INVALID, "id | uploadUrl", "不能为空");
        }

        // 解析目录版本药品excel
        List<MedicalCatalogDetailDO> detailList = new ArrayList<>();
        CatalogRespVO importRespVO = this.analysisCatalogDetailExcel(pageReqVO, detailList);

        if (CollUtil.isEmpty(detailList)) {
            return importRespVO;
        }

        // 批量插入
        selfProxy.batchInsertByXml(detailList);

        // 累加目录的记录总数
        catalogMapper.accumulationCatalogTotalCount(pageReqVO.getId(), detailList.size());
        return importRespVO;
    }

    @Override
    public List<String> listDistinctProjectName(MedicalCatalogProductSearchDto param, int limit) {
        if (param == null || param.getCatalogId() == null) {
            return new ArrayList<>();
        }
        return medicalCatalogDetailMapper.selectSuggestNamesByProductsName(param, limit, 0);
    }

    @Override
    public List<MedicalCatalogDetailDO> searchCatalogProductList(MedicalCatalogProductSearchDto param, int limit) {
        if (param == null || param.getCatalogId() == null) {
            return new ArrayList<>();
        }
        return medicalCatalogDetailMapper.selectProductsByNameSpec(param, limit, 0);
    }

}
