package com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo;

import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.util.Optional;

@Schema(description = "管理后台 - 统一目录明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UnifiedCatalogDetailPageReqVO extends PageParam {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "目录ID")
    private Long catalogId;

    @Schema(description = "是否禁用，默认否")
    private Boolean disable;

    @Schema(description = "混合名称查询（用于全文搜索）")
    private String mixedNameQuery;

    @Schema(description = "备注")
    private String remark;

    // ==================== 编码相关字段 ====================
    
    @Schema(description = "项目编码（监管目录为Long类型标准库ID，医保目录为String类型编码）")
    private String projectCode;

    @Schema(description = "通用名（监管目录使用）")
    private String commonName;

    @Schema(description = "医疗目录名称/项目名称（医保目录使用）")
    private String projectName;

    // ==================== 产品信息字段 ====================
    
    @Schema(description = "品牌名称（通用字段）")
    private String brandName;

    @Schema(description = "规格/型号（监管目录使用）")
    private String spec;

    @Schema(description = "条形码（监管目录使用）")
    private String barcode;

    @Schema(description = "医疗目录类别（医保目录使用）")
    private String projectType;

    @Schema(description = "医疗目录等级（医保目录使用）")
    private String projectLevel;

    // ==================== 企业信息字段 ====================
    
    @Schema(description = "生产厂家/生产企业（通用字段）")
    private String manufacturer;

    @Schema(description = "批准文号（通用字段）")
    private String approvalNumber;

    @Schema(description = "本位码（医保目录使用）")
    private String standardCode;

    // ==================== 类型转换辅助方法 ====================
    
    /**
     * 转换为监管目录分页请求VO
     */
    public RegulatoryCatalogDetailPageReqVO toRegulatoryPageReqVO() {
        RegulatoryCatalogDetailPageReqVO vo = new RegulatoryCatalogDetailPageReqVO();
        vo.setId(this.id);
        vo.setCatalogId(this.catalogId);
        vo.setDisable(this.disable);
        vo.setRemark(this.remark);
        vo.setPageNo(this.getPageNo());
        vo.setPageSize(this.getPageSize());
        
        // 监管目录特有字段
        vo.setProjectCode(NumberUtil.parseLong(this.getProjectCode(), null));
        vo.setCommonName(this.commonName);
        vo.setBrandName(this.brandName);
        vo.setSpec(this.spec);
        vo.setBarcode(this.barcode);
        vo.setManufacturer(this.manufacturer);
        vo.setApprovalNumber(this.approvalNumber);
        
        return vo;
    }

    /**
     * 转换为医保目录分页请求VO
     */
    public MedicalCatalogDetailPageReqVO toMedicalPageReqVO() {
        MedicalCatalogDetailPageReqVO vo = new MedicalCatalogDetailPageReqVO();
        vo.setId(this.id);
        vo.setCatalogId(this.catalogId);
        vo.setDisable(this.disable);
        vo.setMixedNameQuery(this.mixedNameQuery);
        vo.setPageNo(this.getPageNo());
        vo.setPageSize(this.getPageSize());
        
        // 医保目录特有字段
        vo.setProjectCode(this.projectCode);
        vo.setProjectName(this.projectName);
        vo.setBrandName(this.brandName);
        vo.setProjectType(this.projectType);
        vo.setProjectLevel(this.projectLevel);
        vo.setManufacturer(this.manufacturer);
        vo.setApprovalNumber(this.approvalNumber);
        vo.setStandardCode(this.standardCode);

        return vo;
    }

    /**
     * 从监管目录分页请求VO创建统一VO
     */
    public static UnifiedCatalogDetailPageReqVO fromRegulatoryPageReqVO(RegulatoryCatalogDetailPageReqVO vo) {
        UnifiedCatalogDetailPageReqVO unified = new UnifiedCatalogDetailPageReqVO();
        unified.setId(vo.getId());
        unified.setCatalogId(vo.getCatalogId());
        unified.setDisable(vo.getDisable());
        unified.setRemark(vo.getRemark());
        unified.setPageNo(vo.getPageNo());
        unified.setPageSize(vo.getPageSize());
        
        // 监管目录特有字段
        unified.setProjectCode(Optional.ofNullable(vo.getProjectCode()).map(String::valueOf).orElse(null));
        unified.setCommonName(vo.getCommonName());
        unified.setBrandName(vo.getBrandName());
        unified.setSpec(vo.getSpec());
        unified.setBarcode(vo.getBarcode());
        unified.setManufacturer(vo.getManufacturer());
        unified.setApprovalNumber(vo.getApprovalNumber());
        
        return unified;
    }

    /**
     * 从医保目录分页请求VO创建统一VO
     */
    public static UnifiedCatalogDetailPageReqVO fromMedicalPageReqVO(MedicalCatalogDetailPageReqVO vo) {
        UnifiedCatalogDetailPageReqVO unified = new UnifiedCatalogDetailPageReqVO();
        unified.setId(vo.getId());
        unified.setCatalogId(vo.getCatalogId());
        unified.setDisable(vo.getDisable());
        unified.setMixedNameQuery(vo.getMixedNameQuery());
        unified.setPageNo(vo.getPageNo());
        unified.setPageSize(vo.getPageSize());
        
        // 医保目录特有字段
        unified.setProjectCode(vo.getProjectCode());
        unified.setProjectName(vo.getProjectName());
        unified.setBrandName(vo.getBrandName());
        unified.setProjectType(vo.getProjectType());
        unified.setProjectLevel(vo.getProjectLevel());
        unified.setManufacturer(vo.getManufacturer());
        unified.setApprovalNumber(vo.getApprovalNumber());
        unified.setStandardCode(vo.getStandardCode());

        return unified;
    }
}
