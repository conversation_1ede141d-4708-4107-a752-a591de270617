package com.xyy.saas.inquiry.product.server.service.catalog;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.api.catalog.dto.MedicalCatalogProductSearchDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.MedicalCatalogDetailPageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.MedicalCatalogDetailDO;
import java.util.List;

/**
 * 医保目录明细 Service 接口
 *
 * <AUTHOR>
 */
public interface MedicalCatalogDetailService {

    /**
     * 批量插入
     *
     * @param list
     */
    void batchInsertByXml(List<MedicalCatalogDetailDO> list);

    /**
     * 根据目录ID删除明细
     *
     * @param catalogId
     */
    void deleteByCatalogId(Long catalogId);

    /**
     * 修改状态
     *
     */
    boolean updateStatus(MedicalCatalogDetailPageReqVO reqVO);

    /**
     * 获得医保目录明细
     *
     * @param id 编号
     * @return 医保目录明细
     */
    MedicalCatalogDetailDO getCatalogDetail(Long id);

    /**
     * 获得医保目录明细分页
     *
     * @param pageReqVO 分页查询
     * @return 医保目录明细分页
     */
    PageResult<MedicalCatalogDetailDO> getCatalogDetailPage(MedicalCatalogDetailPageReqVO pageReqVO);

    /**
     * 根据目录ID获得医保目录明细列表
     *
     * @param catalogId 目录ID
     * @return 医保目录明细列表
     */
    List<MedicalCatalogDetailDO> getCatalogDetailListByCatalogId(Long catalogId);

    /**
     * 根据项目编码列表获得医保目录明细列表
     *
     * @param catalogId 目录ID
     * @param projectCodes 项目编码列表
     * @return 医保目录明细列表
     */
    List<MedicalCatalogDetailDO> getCatalogDetailListByProjectCodes(Long catalogId, List<String> projectCodes);

    /**
     * 解析医保目录明细Excel
     *
     * @return 解析结果
     */
    CatalogRespVO analysisCatalogDetailExcel(CatalogSaveReqVO reqVO, List<MedicalCatalogDetailDO> detailList);

    /**
     * 导入医保目录明细Excel
     *
     * @return 导入结果
     */
    CatalogRespVO importExcel(CatalogSaveReqVO reqVO);




    /**
     * 根据通用名搜索商品名称推荐词
     *
     * @param param
     * @param limit
     * @return
     */
    List<String> listDistinctProjectName(MedicalCatalogProductSearchDto param, int limit);

    /**
     * 根据通用名+规格搜索商品
     * @param param
     * @param limit
     * @return
     */
    List<MedicalCatalogDetailDO> searchCatalogProductList(MedicalCatalogProductSearchDto param, int limit);

}
