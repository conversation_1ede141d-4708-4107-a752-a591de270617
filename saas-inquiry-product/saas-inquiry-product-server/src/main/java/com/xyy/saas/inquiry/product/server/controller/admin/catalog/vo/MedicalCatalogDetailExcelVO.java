package com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
// EasyExcel 导入模型 @Accessors(chain = true) 会导致读取到的全是 null
// 问题的核心在于 Lombok的@Accessors(chain = true)注解 与EasyExcel的反射机制不兼容。具体表现为：链式Setter的返回值冲突
// Lombok生成的链式Setter方法返回当前对象（return this），而EasyExcel底层使用的CGLib的BeanMap工具类要求Setter方法必须返回void类型才能正确映射数据
@Accessors(chain = false)
public class MedicalCatalogDetailExcelVO {
    
    @ExcelProperty(index = 0, value = "医疗目录编码")
    @Length(max = 64, message = "医疗目录编码长度不能超过64")
    @NotBlank(message = "医疗目录编码不能为空")
    private String projectCode;

    @ExcelProperty(index = 1, value = "医疗目录名称")
    @Length(max = 256, message = "医疗目录名称长度不能超过256")
    @NotBlank(message = "医疗目录名称不能为空")
    private String projectName;

    @ExcelProperty(index = 2, value = "品牌名称")
    @Length(max = 256, message = "品牌名称长度不能超过256")
    private String brandName;
    
    @ExcelProperty(index = 3, value = "医疗目录类别")
    @Length(max = 64, message = "医疗目录类别长度不能超过64")
    private String projectType;

    @ExcelProperty(index = 4, value = "医疗目录等级")
    @Length(max = 10, message = "医疗目录等级长度不能超过10")
    private String projectLevel;

    @ExcelProperty(index = 5, value = "注册规格")
    @Length(max = 512, message = "注册规格长度不能超过512")
    private String regSpec;

    @ExcelProperty(index = 6, value = "规格")
    @Length(max = 512, message = "规格长度不能超过512")
    private String spec;

    @ExcelProperty(index = 7, value = "注册剂型")
    @Length(max = 256, message = "注册剂型长度不能超过256")
    private String regDosageForm;

    @ExcelProperty(index = 8, value = "剂型")
    @Length(max = 256, message = "剂型长度不能超过256")
    private String dosageForm;

    @ExcelProperty(index = 9, value = "最小包装数量")
    @Length(max = 10, message = "最小包装数量长度不能超过10")
    private String minPackageNum;

    @ExcelProperty(index = 10, value = "最小制剂单位")
    @Length(max = 20, message = "最小制剂单位长度不能超过20")
    private String minPreparationUnit;

    @ExcelProperty(index = 11, value = "最小包装单位")
    @Length(max = 20, message = "最小包装单位长度不能超过20")
    private String minPackageUnit;

    @ExcelProperty(index = 12, value = "药品企业")
    @Length(max = 256, message = "药品企业长度不能超过256")
    private String pharmaceuticalEnterprise;

    @ExcelProperty(index = 13, value = "生产企业")
    @Length(max = 256, message = "生产企业长度不能超过256")
    private String manufacturer;

    @ExcelProperty(index = 14, value = "批准文号")
    @Length(max = 256, message = "批准文号长度不能超过256")
    private String approvalNumber;

    @ExcelProperty(index = 15, value = "本位码")
    @Length(max = 64, message = "本位码长度不能超过64")
    private String standardCode;

    @ExcelProperty(index = 16, value = "监管编码")
    @Length(max = 64, message = "监管编码长度不能超过64")
    private String regulatoryCode;

    @ExcelProperty(index = 17, value = "备注")
    @Length(max = 1024, message = "备注长度不能超过1024")
    private String remark;

    @ExcelProperty(index = 18, value = "失败原因")
    private String errMsg;
}
