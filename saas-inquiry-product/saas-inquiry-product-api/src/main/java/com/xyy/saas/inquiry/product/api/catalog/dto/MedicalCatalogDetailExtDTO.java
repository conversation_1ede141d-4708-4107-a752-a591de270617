package com.xyy.saas.inquiry.product.api.catalog.dto;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 医保目录明细 DO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicalCatalogDetailExtDTO implements Serializable {

    /**
     * 自建标准库ID
     */
    @Schema(description = "自建标准库ID")
    private Long stdlibId;

    /**
     * 监管编码（YDID5）
     */
    @Schema(description = "监管编码（YDID5）")
    private String regulatoryCode;

}
