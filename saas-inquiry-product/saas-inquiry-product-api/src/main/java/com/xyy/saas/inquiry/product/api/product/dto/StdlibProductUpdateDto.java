package com.xyy.saas.inquiry.product.api.product.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;


@Schema(description = "管理后台 - 标准库商品批量更新请求 VO")
@Data
public class StdlibProductUpdateDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "id列表", requiredMode = RequiredMode.REQUIRED, example = "[1,2,3]")
    @NotEmpty(message = "id列表不能为空")
    private List<Long> idList;

    @Schema(description = "停用状态", requiredMode = RequiredMode.REQUIRED, example = "true")
    private Boolean disable;

    @Schema(description = "多属性标志", requiredMode = RequiredMode.REQUIRED)
    private ProductFlag productFlag;

}
