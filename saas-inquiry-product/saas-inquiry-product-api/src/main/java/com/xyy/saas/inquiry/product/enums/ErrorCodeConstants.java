package com.xyy.saas.inquiry.product.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * 使用 1-005-000-000 段
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/12 20:47
 */
public interface ErrorCodeConstants {

    ErrorCode PARAM_INVALID = new ErrorCode(1_005_000_000, "参数【{}】非法：{}");
    // ========== 商品基本信息 ==========
    ErrorCode PRODUCT_INFO_NOT_EXISTS = new ErrorCode(1_005_000_001, "商品信息不存在");

    ErrorCode PRODUCT_INFO_DUPLICATE_SHOW_PREF = new ErrorCode(1_005_000_003, "商品编码【{}】重复{}");
    ErrorCode PRODUCT_INFO_DUPLICATE_STDLIB = new ErrorCode(1_005_000_004, "商品标准信息与【{}】重复{}");
    ErrorCode PRODUCT_INFO_CAN_NOT_DELETE = new ErrorCode(1_005_000_005, "商品编码【{}】不能删除");

    ErrorCode PRODUCT_STDLIB_NOT_EXISTS = new ErrorCode(1_005_000_006, "标准库商品信息不存在");
    ErrorCode PRODUCT_STDLIB_STATUS_IN_USING = new ErrorCode(1_005_000_106, "标准库商品正在使用中，不允许变更状态");

    ErrorCode PRODUCT_DELETE_TYPE_UN_SUPPORT = new ErrorCode(1_005_000_007, "商品删除类型不支持");
    ErrorCode PRODUCT_STATUS_UN_SUPPORT_DELETE = new ErrorCode(1_005_000_008, "商品状态不支持删除");

    ErrorCode PRODUCT_PRESENT_DUPLICATED_MID_STDLIB = new ErrorCode(1_005_000_010, "当前提报商品，商品中台已存在，无需重复提报，请检查后重新输入");
    ErrorCode PRODUCT_PRESENT_DUPLICATED_AUDITING = new ErrorCode(1_005_000_011, "当前提报商品，已存在待审核的数据，请耐心等待工作人员审核，无需重复提报");
    ErrorCode PRODUCT_PRESENTED_BY_OTHER = new ErrorCode(1_005_000_012, "当前提报商品，已被其他门店提报，无需重复提报，请耐心等待");
    ErrorCode PRODUCT_PRESENT_DUPLICATED_AUDITING2 = new ErrorCode(1_005_000_013, "当前提报商品，已存在待审核的数据（本店或其他门店），请耐心等待工作人员审核，无需重复提报");
    ErrorCode PRODUCT_PRESENT_CANNOT_EDIT = new ErrorCode(1_005_000_016, "当前提报商品不支持编辑");
    ErrorCode PRODUCT_PRESENT_FAILED = new ErrorCode(1_005_000_017, "提报商品失败：{}");


    ErrorCode PRODUCT_MID_STDLIB_SYNC_FAILED = new ErrorCode(1_005_000_015, "中台标准库数据同步失败，稍后再试。");

    // ========== 质量变更申请操作记录= ==========
    ErrorCode PRODUCT_QUALITY_CHANGE_RECORD_NOT_EXISTS = new ErrorCode(1_005_001_001, "质量变更申请操作记录不存在");
    ErrorCode PRODUCT_QUALITY_CHANGE_DETAIL_NOT_EXISTS = new ErrorCode(1_005_001_002, "质量变更申请明细记录不存在");


    // ========== 售价调整单= ==========
    ErrorCode PRODUCT_PRICE_ADJUSTMENT_RECORD_NOT_EXISTS = new ErrorCode(1_005_002_001, "售价调整单不存在");
    ErrorCode PRODUCT_PRICE_ADJUSTMENT_DETAIL_NOT_EXISTS = new ErrorCode(1_005_002_002, "售价调整单明细不存在");


    // ========== 商品流转记录= ==========
    ErrorCode PRODUCT_TRANSFER_RECORD_NOT_EXISTS = new ErrorCode(1_005_003_001, "商品流转记录不存在");


    // ========== 审批流关联业务 ==========
    ErrorCode BPM_BUSINESS_RELATION_NOT_EXISTS = new ErrorCode(1_005_004_001, "审批流关联业务不存在");
    ErrorCode BPM_BUSINESS_TYPE_NOT_EXISTS = new ErrorCode(1_005_004_002, "审批业务类型【{}】不存在");
    ErrorCode BPM_APPROVE_STATUS_INVALID = new ErrorCode(1_005_004_003, "审批状态【{}】异常");

    // ========== 商品六级分类 ==========
    ErrorCode PRODUCT_CATEGORY_NOT_EXISTS = new ErrorCode(1_005_005_001, "商品六级分类不存在");
    ErrorCode PRODUCT_CATEGORY_EXITS_CHILDREN = new ErrorCode(1_005_005_002, "存在存在子商品六级分类，无法删除");
    ErrorCode PRODUCT_CATEGORY_PARENT_NOT_EXITS = new ErrorCode(1_005_005_003, "父级商品六级分类不存在");
    ErrorCode PRODUCT_CATEGORY_PARENT_ERROR = new ErrorCode(1_005_005_004, "不能设置自己为父商品六级分类");
    ErrorCode PRODUCT_CATEGORY_NAME_DUPLICATE = new ErrorCode(1_005_005_005, "已经存在该分类名称的商品六级分类");
    ErrorCode PRODUCT_CATEGORY_PARENT_IS_CHILD = new ErrorCode(1_005_005_006, "不能设置自己的子分类为父分类");

    // ========== 目录 ==========
    ErrorCode CATALOG_NOT_EXISTS = new ErrorCode(1_005_006_001, "目录不存在");
    ErrorCode CATALOG_ENV_NOT_UPDATE = new ErrorCode(1_005_006_002, "已上线状态目录不允许进行编辑操作");
    ErrorCode CATALOG_RELATION_TENANT_COUNT_IS_NOT_NULL = new ErrorCode(1_005_006_003, "当前版本有门店使用，请将门店绑定删除/批量切换至其他版本后再来进行禁用");
    ErrorCode REGULATORY_CATALOG_DETAIL_DATA_IS_NULL = new ErrorCode(1_005_006_004, "Excel内容不能为空");
    ErrorCode ANALYSIS_CATALOG_EXCEL_ERROR = new ErrorCode(1_005_006_005, "解析Excel失败");
    ErrorCode CATALOG_UPLOAD_URL_IS_EMPTY = new ErrorCode(1_005_006_006, "上传文件链接不能为空");
    ErrorCode UPGRADE_CATALOG_NOT_EXISTS = new ErrorCode(1_005_006_007, "升级目录不存在");
    ErrorCode UPGRADE_CATALOG_VERSION_CODE_NOT_EXISTS = new ErrorCode(1_005_006_008, "升级版本号已存在，同一目录一个小时内只能升级一次");
    ErrorCode CATALOG_DETAIL_NOT_EXISTS = new ErrorCode(1_005_006_009, "目录明细不存在");
    ErrorCode CATALOG_NAME_IS_EXISTS = new ErrorCode(1_005_006_010, "目录名称已存在");
    ErrorCode CATALOG_DETAIL_EXCEL_IS_BLANK = new ErrorCode(1_005_006_011, "上传文件内容不能为空");

    // ========== 医保目录明细 ==========
    ErrorCode MEDICAL_CATALOG_DETAIL_NOT_EXISTS = new ErrorCode(1_005_007_001, "医保目录明细不存在");
    ErrorCode MEDICAL_CATALOG_DETAIL_EXISTS = new ErrorCode(1_005_007_002, "医保目录明细已存在（目录ID+项目编码重复）");
    ErrorCode MEDICAL_CATALOG_DETAIL_DATA_IS_NULL = new ErrorCode(1_005_007_003, "医保目录明细Excel内容不能为空");

}
