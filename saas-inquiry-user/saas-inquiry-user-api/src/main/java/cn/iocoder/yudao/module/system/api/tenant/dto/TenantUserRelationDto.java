package cn.iocoder.yudao.module.system.api.tenant.dto;

import cn.iocoder.yudao.module.system.enums.common.SexEnum;
import com.xyy.saas.inquiry.enums.user.UserStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/13 20:27
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class TenantUserRelationDto implements Serializable {

    /**
     * 关系id
     */
    private Long id;
    /**
     * 用户id
     */
    @Schema(description = "用户id", example = "111")
    @NotNull(message = "用户id不能为空")
    private Long userId;


    @Schema(description = "门店id", example = "111")
    private Long tenantId;

    @Schema(description = "总部门店id", example = "111")
    private Long headTenantId;

    @Schema(description = "门店类型", example = "111")
    private Integer tenantType;


    @Schema(description = "手机号", example = "15926350001")
    private String mobile;

    /**
     * 用户账号
     */
    private String username;
    /**
     * 姓名
     */
    private String nickname;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 用户性别
     * <p>
     * 枚举类 {@link SexEnum}
     */
    private Integer sex;
    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 部门编号
     */
    @Schema(description = "部门编号", example = "我是一个用户")
    private Long deptId;

    /**
     * 岗位编号数组
     */
    @Schema(description = "岗位编号数组", example = "1")
    private Set<Long> postIds;

    /**
     * 入职时间
     */
    @Schema(description = "入职时间")
    private LocalDateTime joinTime;

    /**
     * 离职时间
     */
    @Schema(description = "入职时间")
    private LocalDateTime resignationTime;

    /**
     * 员工状态 {@link UserStatusEnum}
     */
    @Schema(description = "员工状态")
    private Integer status;

    /**
     * 是否需要打卡 (0是 1否) {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "是否需要打卡 (0是 1否)")
    private Integer needClockIn;

    /**
     * 是否打卡 (0是 1否) {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer clockInStatus;

}
