package cn.iocoder.yudao.module.system.enums.sms;

import cn.hutool.core.util.ArrayUtil;
import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 用户短信验证码发送场景的枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SmsSceneEnum implements IntArrayValuable {

    USER_LOGIN(1, "user-sms-login", "用户 - 手机号登陆"),

    USER_UPDATE_MOBILE(2, "user-update-mobile", "用户 - 修改手机"),
    USER_UPDATE_PASSWORD(3, "user-update-password", "用户 - 修改密码"),
    USER_RESET_PASSWORD(4, "user-reset-password", "用户 - 忘记密码"),

    TENANT_UPDATE_TRANSFERS(5, "tenant-update-transfers", "门店 - 门店转让"),

    TENANT_BIND_OLD_HY(6, "tenant-bind-old-hy", "门店 - 绑定老荷叶"),
    ;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(SmsSceneEnum::getScene).toArray();

    /**
     * 验证场景的编号
     */
    private final Integer scene;
    /**
     * 模版编码
     */
    private final String templateCode;
    /**
     * 描述
     */
    private final String description;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static SmsSceneEnum getCodeByScene(Integer scene) {
        return ArrayUtil.firstMatch(sceneEnum -> sceneEnum.getScene().equals(scene),
            values());
    }

}
