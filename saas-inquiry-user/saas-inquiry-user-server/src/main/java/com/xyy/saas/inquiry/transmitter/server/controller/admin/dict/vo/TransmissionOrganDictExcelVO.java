package com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.nacos.common.utils.NumberUtils;
import com.xyy.saas.inquiry.pojo.excel.ImportExcelVoDto;
import com.xyy.saas.inquiry.util.excel.validator.DictValid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户 Excel 导入 VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class TransmissionOrganDictExcelVO extends ImportExcelVoDto {


    @ExcelProperty(value = "字典标签", index = 0)
    @NotBlank(message = "字典标签不能为空")
    @Size(max = 64, message = "字典标签超出最大长度64限制")
    private String label;

    @ExcelProperty(value = "字典值", index = 1)
    @NotBlank(message = "字典值不能为空")
    @Size(max = 64, message = "字典值超出最大长度64限制")
    private String value;

    @ExcelProperty(value = "父字典值", index = 2)
    @Size(max = 64, message = "父字典值超出最大长度64限制")
    private String parentValue;

    @ExcelProperty(value = "字典外码", index = 3)
    @Size(max = 64, message = "字典外码超出最大长度64限制")
    private String outerValue;

    @ExcelProperty(value = "状态", index = 4, converter = DictConvert.class)
    @DictValid(dictType = DictTypeConstants.COMMON_STATUS, message = "无法找到对应的状态")
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private String status;

    @ExcelProperty(value = "末级节点", index = 5)
    private String endNode;

    @ExcelProperty(value = "排序", index = 6)
    private String sort;

    @ExcelProperty(value = "备注", index = 7)
    @Size(max = 64, message = "备注超出最大长度64限制")
    private String remark;


    public Integer status() {
        return NumberUtils.isDigits(status) ? NumberUtils.toInt(status) : null;
    }

    public Integer endNode() {
        return NumberUtils.isDigits(endNode) ? NumberUtils.toInt(endNode) : null;
    }

    public Integer sort() {
        return NumberUtils.isDigits(sort) ? NumberUtils.toInt(sort) : null;
    }


    public void setLabel(String label) {
        this.label = label;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public void setParentValue(String parentValue) {
        this.parentValue = parentValue;
    }

    public void setOuterValue(String outerValue) {
        this.outerValue = outerValue;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setEndNode(String endNode) {
        this.endNode = endNode;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
