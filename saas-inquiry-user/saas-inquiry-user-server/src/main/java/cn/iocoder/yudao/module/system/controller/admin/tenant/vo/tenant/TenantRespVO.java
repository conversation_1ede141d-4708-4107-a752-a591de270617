package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.biz.TenantBizRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateRespVO;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.pojo.tenant.TenantExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 门店 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TenantRespVO {

    @Schema(description = "门店编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "14411")
    private Long id;

    @Schema(description = "门店编码", example = "MD100001")
    @ExcelProperty("门店编码")
    private String pref;

    /**
     * 租户类型（1-单店 2连锁门店 3连锁总部）
     */
    @DictFormat(DictTypeConstants.TENANT_TYPE)
    @ExcelProperty(value = "门店类型", converter = DictConvert.class)
    private Integer type;

    @Schema(description = "连锁总部ID", example = "0,1")
    private String bizTypes;

    @Schema(description = "业务线", example = "智慧脸")
    @ExcelProperty(value = "业务线")
    private String bizTypeNames;

    @Schema(description = "查询业务线", example = "1")
    @ExcelProperty(value = "查询业务线", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.SYSTEM_BIZ_TYPE)
    private Integer bizType;

    @Schema(description = "业务线门店类型", example = "1")
    @DictFormat(DictTypeConstants.INQUIRY_TENANT_TYPE)
    @ExcelProperty(value = "业务线门店类型", converter = DictConvert.class)
    private Integer bizTenantType;

    @Schema(description = "连锁总部ID", example = "MD100001")
    private Long bizHeadTenantId;

    @Schema(description = "连锁总部", example = "MD100001")
    @ExcelProperty("连锁总部")
    private String bizHeadTenantName;

    @Schema(description = "连锁总部ID", example = "MD100001")
    private Long headTenantId;

    @Schema(description = "连锁总部", example = "MD100001")
    private String headTenantName;

    @Schema(description = "门店名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("门店名")
    private String name;


    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("省")
    private String province;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("市")
    private String city;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("区")
    private String area;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "药店地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("药店地址")
    private String address;

    @Schema(description = "联系人的用户id", example = "11333")
    private Long contactUserId;

    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("联系人")
    private String contactName;

    @Schema(description = "联系手机")
    @ExcelProperty("联系手机")
    private String contactMobile;


    @Schema(description = "营业执照名称", example = "张三")
    @ExcelProperty("营业执照名称")
    private String businessLicenseName;

    @Schema(description = "营业执照号")
    @ExcelProperty("营业执照号")
    private String businessLicenseNumber;


    @Schema(description = "门店状态（0正常 1停用）", example = "1")
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    @ExcelProperty(value = "门店状态", converter = DictConvert.class)
    private Integer status;

    /**
     * {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "问诊系统业务类型开通", example = "0")
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    @ExcelProperty(value = "问诊系统", converter = DictConvert.class)
    private Integer wzBizTypeStatus;

    @Schema(description = "账号数量", example = "26469")
    private Integer wzAccountCount;


    @Schema(description = "智慧脸业务线类型开通", example = "2")
    @ExcelProperty(value = "智慧脸系统", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Integer zhlBizTypeStatus;

    @Schema(description = "智慧脸账号数量", example = "17503")
    private Integer zhlAccountCount;


    @Schema(description = "环境标识：prod-真实数据；test-测试数据；show-线上演示数据")
    @ExcelProperty(value = "环境标识", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.SYSTEM_ENV_TAG)
    private String envTag;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "资质证件信息")
    private List<TenantCertificateRespVO> certificates;

    @Schema(description = "租户业务信息")
    private List<TenantBizRelationRespVO> bizRelations;

    @Schema(description = "租户拓展字段")
    private TenantExtDto ext;


    @Schema(description = "门店是否存在当前选择")
    private boolean existsOptionType;

    @Schema(description = "三方服务商名称集合")
    private List<String> transmissionOrganNameList;

    @Schema(description = "是否绑定老荷叶门店")
    private boolean bindOldHy;

    public boolean isBindOldHy() {
        return ext != null && StringUtils.isNotBlank(ext.getOrganSign());
    }
}
