package cn.iocoder.yudao.module.system.controller.admin.tenant;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSimpleRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantTransfersVO;
import cn.iocoder.yudao.module.system.convert.tenant.TenantConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.service.tenant.TenantHeadService;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import cn.iocoder.yudao.module.system.service.user.UserMobileBusinessService;
import com.xyy.saas.inquiry.enums.tenant.BindPlatformEnum;
import com.xyy.saas.inquiry.pojo.TenantDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 门店")
@RestController
@RequestMapping("/system/tenant")
public class TenantController {

    @Resource
    private TenantService tenantService;

    @Resource
    private TenantHeadService tenantHeadService;

    @Resource
    private UserMobileBusinessService userMobileBusinessService;

    @GetMapping("/get-id-by-name")
    @PermitAll
    @Operation(summary = "使用门店名，获得门店编号", description = "登录界面，根据用户的门店名，获得门店编号")
    @Parameter(name = "name", description = "门店名", required = true, example = "1024")
    public CommonResult<Long> getTenantIdByName(@RequestParam("name") String name) {
        TenantDO tenant = tenantService.getTenantByName(name);
        return success(tenant != null ? tenant.getId() : null);
    }

    @GetMapping("/get-list-by-userId")
    @PermitAll
    @Operation(summary = "使用当前userId，获得门店列表", description = "登录界面，使用当前userId，获得门店编号")
    @Parameter(name = "userId", description = "userId", required = true, example = "2")
    public CommonResult<List<TenantSimpleRespVO>> getTenantListByUserId(@RequestParam("userId") Long userId) {
        List<TenantSimpleRespVO> list = tenantService.getTenantListByUserId(userId);
        return success(list);
    }

    @GetMapping("/get-by-id")
    @PermitAll
    @Operation(summary = "使用当前租户ID，获得门店信息", description = "内部调用")
    @Parameter(name = "tenantId", description = "tenantId", required = true, example = "2")
    public CommonResult<TenantDto> getTenantById(@RequestParam("tenantId") Long tenantId) {
        TenantDO tenant = tenantService.getTenant(tenantId);
        TenantDto tenantDto = TenantConvert.INSTANCE.convertDto(tenant);
        return success(tenantDto);
    }

    @GetMapping("/get-by-pref")
    @PermitAll
    @Operation(summary = "使用当前门店编码，获得门店信息", description = "内部调用")
    @Parameter(name = "pref", description = "pref", required = true, example = "2")
    public CommonResult<TenantDto> getTenantById(@RequestParam("pref") String pref) {
        TenantDO tenant = tenantService.getTenantByPref(pref);
        if(tenant == null){
            return success(null);
        }
        TenantDto tenantDto = TenantConvert.INSTANCE.convertDto(tenant);
        return success(tenantDto);
    }

    @PostMapping("/create")
    @Operation(summary = "创建门店")
    @PreAuthorize("@ss.hasPermission('system:tenant:create')")
    public CommonResult<Long> createTenant(@Valid @RequestBody TenantSaveReqVO createReqVO) {
        return success(tenantService.createTenant(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新门店")
    @PreAuthorize("@ss.hasPermission('system:tenant:update')")
    public CommonResult<TenantRespVO> updateTenant(@Valid @RequestBody TenantSaveReqVO updateReqVO) {
        TenantDO tenant = tenantService.updateTenant(updateReqVO);
        return success(TenantConvert.INSTANCE.convertDo2Vo(tenant));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除门店")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:tenant:delete')")
    public CommonResult<Boolean> deleteTenant(@RequestParam("id") Long id) {
        tenantService.deleteTenant(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得门店")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasAnyPermissions('system:tenant:query','drugstore:info:query')")
    public CommonResult<TenantRespVO> getTenant(@RequestParam("id") Long id) {
        TenantRespVO tenantVo = tenantService.getTenantVo(id);
        return success(tenantVo);
    }

    @GetMapping("/page")
    @Operation(summary = "获得门店分页")
    @PreAuthorize("@ss.hasPermission('system:tenant:query')")
    public CommonResult<PageResult<TenantRespVO>> getTenantPage(@Valid TenantPageReqVO pageVO) {
        PageResult<TenantRespVO> pageResult = tenantService.getTenantPage(pageVO);
        return success(pageResult);
    }


    @GetMapping("/head-tenant-list")
    @Operation(summary = "总部获得所有门店列表")
    // @PreAuthorize("@ss.hasPermission('system:tenant:query')")
    public CommonResult<List<TenantRespVO>> getHeadTenantList() {
        List<TenantRespVO> list = tenantHeadService.getTenantsByHeadId();
        return success(list);
    }


    @GetMapping("/export-excel")
    @Operation(summary = "导出门店 Excel")
    @PreAuthorize("@ss.hasPermission('system:tenant:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTenantExcel(@Valid TenantPageReqVO exportReqVO,
        HttpServletResponse response) throws IOException {
        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TenantRespVO> list = tenantService.getTenantPage(exportReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "门店.xls", "数据", TenantRespVO.class,
            list);
    }


    @PutMapping("/transfers")
    @Operation(summary = "门店转让")
    @PreAuthorize("@ss.hasPermission('system:tenant:update')")
    public CommonResult<Boolean> transfers(@Valid @RequestBody TenantTransfersVO transfersVO) {
        transfersVO.setTenantId(Optional.ofNullable(transfersVO.getTenantId()).orElse(TenantContextHolder.getRequiredTenantId()));
        userMobileBusinessService.transfers(transfersVO);
        return success(true);
    }

    @PostMapping("/updateBindPlatform")
    @Operation(summary = "更新门店绑定平台")
    @PermitAll
    public CommonResult<Boolean> updateBindPlatform( @RequestBody TenantSaveReqVO updateReqVO) {
        if(updateReqVO == null || updateReqVO.getId() ==null || updateReqVO.getBindPlatform() == null){
            return success(false);
        }
        if(Arrays.stream(BindPlatformEnum.ARRAYS).noneMatch(e-> Objects.equals(updateReqVO.getBindPlatform(), e))){
            return success(false);
        }
        tenantService.updateBindPlatform(updateReqVO);
        return success(true);
    }


}
