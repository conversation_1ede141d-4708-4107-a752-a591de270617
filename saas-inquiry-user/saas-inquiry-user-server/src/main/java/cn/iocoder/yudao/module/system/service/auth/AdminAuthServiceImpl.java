package cn.iocoder.yudao.module.system.service.auth;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.API_LOGIN_CONFIG_ERROR;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.API_LOGIN_SIGN_ERROR;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTH_LOGIN_CAPTCHA_CODE_ERROR;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTH_LOGIN_USER_DISABLED;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTH_MOBILE_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTH_THIRD_LOGIN_NOT_BIND;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.OA_LOGIN_QUERY_USER_ERROR;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_ADMIN_NOT_LOGOFF;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_USER_RELATION_NOT_EXISTS_LOGIN;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_BIZ_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.common.util.monitor.TracerUtils;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.common.util.validation.ValidationUtils;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.system.api.logger.dto.LoginLogCreateReqDTO;
import cn.iocoder.yudao.module.system.api.sms.SmsCodeApi;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserRespDTO;
import cn.iocoder.yudao.module.system.api.user.dto.OASsoConfigDto;
import cn.iocoder.yudao.module.system.api.user.dto.SSOLoginResult;
import cn.iocoder.yudao.module.system.api.user.dto.SSOUser;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthLoginApiReqVO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthLoginReqVO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthLoginTenantReqVO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthSmsLoginReqVO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthSmsSendReqVO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthSocialLoginReqVO;
import cn.iocoder.yudao.module.system.controller.admin.oauth2.vo.token.OAuth2AccessTokenPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSimpleRespVO;
import cn.iocoder.yudao.module.system.convert.auth.AuthConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.dal.redis.common.UserLockRedisDAO;
import cn.iocoder.yudao.module.system.enums.logger.LoginLogTypeEnum;
import cn.iocoder.yudao.module.system.enums.logger.LoginResultEnum;
import cn.iocoder.yudao.module.system.enums.oauth2.OAuth2ClientConstants;
import cn.iocoder.yudao.module.system.enums.sms.SmsSceneEnum;
import cn.iocoder.yudao.module.system.service.dict.DictDataService;
import cn.iocoder.yudao.module.system.service.logger.LoginLogService;
import cn.iocoder.yudao.module.system.service.member.MemberService;
import cn.iocoder.yudao.module.system.service.oa.OaWhiteListService;
import cn.iocoder.yudao.module.system.service.oauth2.OAuth2TokenService;
import cn.iocoder.yudao.module.system.service.social.SocialUserService;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import cn.iocoder.yudao.module.system.util.SSOUtil;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Maps;
import com.xingyuv.captcha.model.common.ResponseModel;
import com.xingyuv.captcha.model.vo.CaptchaVO;
import com.xingyuv.captcha.service.CaptchaService;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.user.UserAccountStatusEnum;
import com.xyy.saas.inquiry.user.server.dal.dataobject.tenant.TenantThirdAppDO;
import com.xyy.saas.inquiry.user.server.property.LoginKickOffProperties;
import com.xyy.saas.inquiry.user.server.service.tenant.TenantThirdAppService;
import com.xyy.saas.inquiry.util.RedisUtils;
import jakarta.annotation.Resource;
import jakarta.validation.Validator;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

/**
 * Auth Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AdminAuthServiceImpl implements AdminAuthService {

    @Resource
    private AdminUserService userService;
    @Resource
    private LoginLogService loginLogService;
    @Resource
    private OAuth2TokenService oauth2TokenService;
    @Resource
    private SocialUserService socialUserService;
    @Resource
    private MemberService memberService;
    @Resource
    private Validator validator;
    @Resource
    private CaptchaService captchaService;
    @Resource
    private SmsCodeApi smsCodeApi;
    @Resource
    private TenantService tenantService;
    @Resource
    private DictDataService dictDataService;
    @Resource
    private OaWhiteListService oaWhiteListService;
    @Resource
    private UserLockRedisDAO userLockRedisDAO;

    @Resource
    private TenantThirdAppService thirdAppService;

    /**
     * 验证码的开关，默认为 true
     */
    @Value("${yudao.captcha.enable:false}")
    private Boolean captchaEnable;

    /**
     * api授权登陆防重放攻击间隔，单位秒，默认为 300s
     */
    @Value("${yudao.api.login.replay.interval:300}")
    private int apiLoginReplayInterval;

    @Resource
    private LoginKickOffProperties loginKickOffProperties;

    @Resource
    private MemberUserService memberUserService;

    /**
     * @param username 账号 (正则匹配 支持手机号登录)
     * @return
     */
    @Override
    public AdminUserDO authenticate(String username, String password) {
        // 校验锁定
        userLockRedisDAO.isLocked(username);
        boolean isMobile = cn.hutool.core.lang.Validator.isMobile(username);
        LoginLogTypeEnum logTypeEnum = isMobile ? LoginLogTypeEnum.LOGIN_MOBILE : LoginLogTypeEnum.LOGIN_USERNAME;
        // 校验账号是否存在
        AdminUserDO user = null;
        if (isMobile) {
            user = userService.getUserByMobileSystem(username);
        }
        if (user == null) {
            user = userService.getUserByUsername(username);
        }
        if (user == null) {
            createLoginLog(null, username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        if (!userService.isPasswordMatch(password, user.getPassword())) {
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        // 校验是否禁用
        if (CommonStatusEnum.isDisable(user.getStatus())) {
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }
        return user;
    }

    @Override
    @TenantIgnore
    public AuthLoginRespVO login(AuthLoginReqVO reqVO) {
        // 校验验证码
        validateCaptcha(reqVO);

        // 使用账号密码，进行登录
        AdminUserDO user = authenticate(reqVO.getUsername(), reqVO.getPassword());

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getId(), reqVO.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME, reqVO.getClientChannelType());
    }

    @Override
    public void sendSmsCode(AuthSmsSendReqVO reqVO) {
        // 登录场景，验证是否存在
        if (userService.getUserByMobileSystem(reqVO.getMobile()) == null) {
            throw exception(AUTH_MOBILE_NOT_EXISTS);
        }
        // 发送验证码
        smsCodeApi.sendSmsCode(AuthConvert.INSTANCE.convert(reqVO).setCreateIp(getClientIP()));
    }

    @Override
    @TenantIgnore
    public AuthLoginRespVO smsLogin(AuthSmsLoginReqVO reqVO) {
        // 验证锁定
        userLockRedisDAO.isLocked(reqVO.getMobile());
        // 校验验证码
        smsCodeApi.useSmsCode(AuthConvert.INSTANCE.convert(reqVO, SmsSceneEnum.USER_LOGIN.getScene(), getClientIP()));

        // 获得用户信息
        AdminUserDO user = userService.getUserByMobileSystem(reqVO.getMobile());
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        // 校验是否禁用
        if (CommonStatusEnum.isDisable(user.getStatus())) {
            createLoginLog(user.getId(), reqVO.getMobile(), LoginLogTypeEnum.LOGIN_SMS, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getId(), reqVO.getMobile(), LoginLogTypeEnum.LOGIN_MOBILE, reqVO.getClientChannelType());
    }

    private void createLoginLog(Long userId, String username,
        LoginLogTypeEnum logTypeEnum, LoginResultEnum loginResult) {
        createLoginLog(userId, username, logTypeEnum, loginResult, null);
    }

    private void createLoginLog(Long userId, String username,
        LoginLogTypeEnum logTypeEnum, LoginResultEnum loginResult, Integer userType) {
        // 插入登录日志
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logTypeEnum.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        // 不传默认为admin
        reqDTO.setUserType(userType == null ? getUserType().getValue() : userType);
        reqDTO.setUsername(username);
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(ServletUtils.getClientIP());
        reqDTO.setResult(loginResult.getResult());
        loginLogService.createLoginLog(reqDTO);
        // 更新最后登录时间
        if (userId != null && Objects.equals(LoginResultEnum.SUCCESS.getResult(), loginResult.getResult())) {
            userService.updateUserLogin(userId, ServletUtils.getClientIP());
        }
        // 账号密码登录失败的，redis记录锁定
        if (Objects.equals(LoginResultEnum.BAD_CREDENTIALS.getResult(), loginResult.getResult())) {
            userLockRedisDAO.lockRecord(username);
        }
    }


    @Override
    public AuthLoginRespVO socialLogin(AuthSocialLoginReqVO reqVO) {
        // 使用 code 授权码，进行登录。然后，获得到绑定的用户编号
        SocialUserRespDTO socialUser = socialUserService.getSocialUserByCode(UserTypeEnum.ADMIN.getValue(), reqVO.getType(),
            reqVO.getCode(), reqVO.getState());
        if (socialUser == null || socialUser.getUserId() == null) {
            throw exception(AUTH_THIRD_LOGIN_NOT_BIND);
        }

        // 获得用户
        AdminUserDO user = userService.getUserStore(socialUser.getUserId());
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_SOCIAL, reqVO.getClientChannelType());
    }

    @VisibleForTesting
    void validateCaptcha(AuthLoginReqVO reqVO) {
        // 如果验证码关闭，则不进行校验
        if (!captchaEnable) {
            return;
        }
        // 校验验证码
        ValidationUtils.validate(validator, reqVO, AuthLoginReqVO.CodeEnableGroup.class);
        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaVerification(reqVO.getCaptchaVerification());
        ResponseModel response = captchaService.verification(captchaVO);
        // 验证不通过
        if (!response.isSuccess()) {
            // 创建登录失败日志（验证码不正确)
            createLoginLog(null, reqVO.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME, LoginResultEnum.CAPTCHA_CODE_ERROR);
            throw exception(AUTH_LOGIN_CAPTCHA_CODE_ERROR, response.getRepMsg());
        }
    }

    private AuthLoginRespVO createTokenAfterLoginSuccess(Long userId, String username, LoginLogTypeEnum logType, Integer clientChannelTypeEnum) {
        return createTokenAfterLoginSuccess(userId, getUserType().getValue(), username, logType, null, clientChannelTypeEnum);
    }

    private AuthLoginRespVO createTokenAfterLoginSuccess(Long userId, Integer userType, String username, LoginLogTypeEnum logType, Long tenantId, Integer clientChannelTypeEnum) {
        // 插入登陆日志
        createLoginLog(userId, username, logType, LoginResultEnum.SUCCESS, userType);

        List<TenantSimpleRespVO> tenantList = null;
        if (tenantId != null) {
            TenantContextHolder.setTenantId(tenantId);
        } else {
            // 判断是否多门店
            tenantList = tenantService.getTenantListByUserId(userId);
            if (CollUtil.isEmpty(tenantList)) {
                throw exception(TENANT_USER_RELATION_NOT_EXISTS_LOGIN);
            }
            if (tenantList.size() == 1) { // 仅一个门店时设置默认
                TenantContextHolder.setTenantId(tenantList.getFirst().getId());
            }
        }
        // 创建访问令牌
        // Integer userType = getUserType().getValue();
        // String clientId = OAuth2ClientConstants.CLIENT_ID_DEFAULT;
        ClientChannelTypeEnum clientChannelType = ClientChannelTypeEnum.fromCode(clientChannelTypeEnum);
        String clientId = Optional.ofNullable(clientChannelType).map(ClientChannelTypeEnum::getSuffix).orElse(OAuth2ClientConstants.CLIENT_ID_DEFAULT);
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.createAccessToken(userId, userType, clientId, null);
        // 构建返回结果
        AuthLoginRespVO loginRespVO = AuthConvert.INSTANCE.convert(accessTokenDO);

        if (CollUtil.isNotEmpty(tenantList) && CollUtil.size(tenantList) > 1) {
            return loginRespVO.setUserId(userId).setMultiTenant(true).setTenantList(tenantList);
        }

        TenantDO tenant = tenantService.getTenant(TenantContextHolder.getTenantId());
        if(tenant == null || (tenant.getWzBizTypeStatus() == 1 && tenant.getZhlBizTypeStatus() == 1)) {
            throw exception(TENANT_BIZ_NOT_EXISTS);
        }

        // pc和app端踢下线, 这里增加配置文件，指定设备类型、用户是否踢下线
        Optional.ofNullable(loginKickOffProperties).ifPresent(properties -> {
            String deviceType = clientChannelType == null ? "" : clientChannelType.getSuffix();
            // 判断是否踢下线
            if (!properties.needKickOff(deviceType, userId)) {
                return;
            }
            // 踢下线，同账号限制单台设备(区分客户端类型)登陆，同一个用户只能在一台PC/APP设备上登录，但是可以同时在PC和APP上登录
            OAuth2AccessTokenPageReqVO auth2AccessTokenPageReqVO = new OAuth2AccessTokenPageReqVO().setUserId(userId).setUserType(userType).setClientId(clientId);
            oauth2TokenService.getAccessTokenPage(auth2AccessTokenPageReqVO).getList().forEach(t -> {
                // 排除当前登陆的token
                if (!Objects.equals(t.getId(), accessTokenDO.getId())) {
                    logout(t.getAccessToken(), LoginLogTypeEnum.LOGOUT_KICK.getType());
                }
            });
        });
        // 设置门店类型
        loginRespVO.setTenantType(tenant.getType());
        return loginRespVO;
    }

    @Override
    public AuthLoginRespVO refreshToken(String refreshToken) {
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.refreshAccessToken(refreshToken, OAuth2ClientConstants.CLIENT_ID_DEFAULT);
        return AuthConvert.INSTANCE.convert(accessTokenDO);
    }

    @Override
    public void logout(String token, Integer logType) {
        // 删除访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.removeAccessToken(token);
        if (accessTokenDO == null) {
            return;
        }
        // 删除成功，则记录登出日志
        createLogoutLog(accessTokenDO.getUserId(), accessTokenDO.getUserType(), logType);
    }

    @Override
    public void logout(Long userId, LoginLogTypeEnum logType) {
        // 跟据userId查询用户在线token 和 refreshToken 并删除
        int removeRow = oauth2TokenService.removeAccessTokenByUserId(userId);
        if (removeRow < 1) {
            return;
        }
        // 删除成功，则记录登出日志
        createLogoutLog(userId, UserTypeEnum.ADMIN.getValue(), logType.getType());
    }


    @Override
    public void logout(Long userId, List<Long> tenantIds, LoginLogTypeEnum logType) {
        oauth2TokenService.removeAccessTokenByUserIdTenants(userId, tenantIds);

        // 删除成功，则记录登出日志
        createLogoutLog(userId, UserTypeEnum.ADMIN.getValue(), logType.getType());
    }

    /**
     * 注销用户
     *
     * @param token
     */
    @Override
    public void logoff(String token) {
        // 检查当前用户是否租户管理员角色
        checkTenantAdmin(token);
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.removeAccessToken(token);
        if (accessTokenDO == null) {
            return;
        }
        // 调用登出
        logout(token, LoginLogTypeEnum.LOGOUT_OFF.getType());
        // 修改用户状态
        userService.updateUserStatusSystem(accessTokenDO.getUserId(), UserAccountStatusEnum.QUIT.getCode());
    }

    /**
     * 校验租户管理员
     *
     * @param token
     */
    private void checkTenantAdmin(String token) {
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.getAccessToken(token);
        if (ObjectUtil.isEmpty(accessTokenDO)) {
            return;
        }
        List<TenantDO> tenantDOList = tenantService.getTenantByAdminUserId(accessTokenDO.getUserId());
        if (CollUtil.isNotEmpty(tenantDOList)) {
            throw exception(TENANT_ADMIN_NOT_LOGOFF);
        }
    }

    @Override
    public AuthLoginRespVO loginWithTenantId(AuthLoginTenantReqVO reqVO) {
        // 查询当前token
        OAuth2AccessTokenDO oldTokenDO = oauth2TokenService.getAccessToken(reqVO.getToken());
        TenantContextHolder.setTenantId(reqVO.getTenantId());
        // 创建访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.createAccessToken(reqVO.getUserId(), oldTokenDO.getUserType(),
            OAuth2ClientConstants.CLIENT_ID_DEFAULT, null);
        // 多门店二次最终登录需要将第一次用户名密码登录的token清除掉
        if (ObjectUtil.isNotEmpty(oldTokenDO)) {
            oauth2TokenService.removeAccessToken(reqVO.getToken());
        }
        // 构建返回结果
        // AuthLoginRespVO convert = AuthConvert.INSTANCE.convert(accessTokenDO);

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(reqVO.getUserId(), accessTokenDO.getUserType(), reqVO.getUsername(), LoginLogTypeEnum.LOGIN_MINI_PROGRAM, reqVO.getTenantId(),
            reqVO.getClientChannelType());
    }


    /**
     * 可添加能登陆放行的白名单 不然所有oa账号用户都可登录，或者登录后不给角色权限，需要超级管理员设置后重新登录才可进入
     *
     * @param reqVO
     * @return
     */
    @Override
    public AuthLoginRespVO OALogin(AuthLoginReqVO reqVO) {
        // 校验白名单
        oaWhiteListService.getOaWhiteRequiredByUserName(reqVO.getUsername(), BizTypeEnum.HYWZ);

        // 1.查询oa登录所需的字典系统配置
        OASsoConfigDto oaSsoConfigDto = dictDataService.getOAConfigDictDto();
        // 2.调用oa接口换取ticket
        SSOLoginResult ssoLoginResult = SSOUtil.getTicket(reqVO.getUsername(), reqVO.getPassword(), oaSsoConfigDto.getServiceUrl(), oaSsoConfigDto.getSsoUrl() + oaSsoConfigDto.getLoginPath());
        if (!ssoLoginResult.isSuccess()) {
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        // 3. 根据 ticket 查询用户信息完成登录
        SSOUser ssoUser = SSOUtil.querySSOUser(reqVO.getUsername(), ssoLoginResult.getTgc(), oaSsoConfigDto.getOaBusinessUrl() + oaSsoConfigDto.getQueryAccountPath());
        if (Objects.isNull(ssoUser)) {
            throw exception(OA_LOGIN_QUERY_USER_ERROR);
        }
        // 4.创建或者更新用户信息 TODO 考虑 分配初始角色
        Long userId = userService.handleOALoginUser(ssoUser);
        // 5.创建访问令牌
        return createTokenAfterLoginSuccess(userId, reqVO.getUsername(), LoginLogTypeEnum.LOGIN_OA, reqVO.getClientChannelType());
    }


    /**
     * 根据三方应用授权登录
     * <p>
     * 1.根据ak查询 三方应用配置 记录 2.校验timestamp 与当前时间间隔不大于5min （apollo可配置） 3.校验nonce是否重复，nonce存储redis（过期时间大于timestamp间隔） 4.验证 md5(sk + nonce + timestamp) 与 sign 是否一致 5.验签成功后，查询 三方应用配置 对应的tenantId，获取该租户的系统管理员，以管理员身份登陆
     *
     * @param reqVO
     * @return
     */
    @Override
    public AuthLoginRespVO apiLogin(AuthLoginApiReqVO reqVO) {
        String appKey = reqVO.getAppKey();
        String key = "api-login:" + appKey;
        // 0. 从redis中获取缓存的token
        if (RedisUtils.get(key) instanceof String token) {
            return JSONUtil.toBean(token, AuthLoginRespVO.class);
        }
        // TODO 限流: 限制同一appKey的请求频率，防止恶意请求

        // 1. 根据appKey查询三方应用配置
        TenantThirdAppDO thirdAppDO = thirdAppService.getByAppKey(appKey);
        if (thirdAppDO == null
            || !CommonStatusEnum.isEnable(thirdAppDO.getStatus())) {
            throw exception(API_LOGIN_CONFIG_ERROR);
        }

        // 2. 校验timestamp 与当前时间间隔不大于5min （apollo可配置）
        Long timestamp = reqVO.getTimestamp();
        if (timestamp == null || timestamp < System.currentTimeMillis() - apiLoginReplayInterval * 1000L) {
            throw exception(API_LOGIN_SIGN_ERROR);
        }

        // 3. 校验nonce是否重复，nonce存储redis（过期时间大于timestamp间隔）
        String nonce = reqVO.getNonce();
        // 延迟过期：防重放攻击间隔 + 10s
        boolean nonceCached = userLockRedisDAO.setNonceExpire(nonce, apiLoginReplayInterval / 1000 + 10);
        if (!nonceCached) {
            throw exception(API_LOGIN_SIGN_ERROR);
        }

        // 4. 验证 md5(sk + nonce + timestamp) 与 sign 是否一致
        String appSecret = thirdAppDO.getAppSecret();
        String signHex = DigestUtils.md5DigestAsHex((appSecret + nonce + timestamp).getBytes()).toUpperCase();
        if (!Objects.equals(signHex, reqVO.getSign())) {
            throw exception(API_LOGIN_SIGN_ERROR);
        }

        Long tenantId = thirdAppDO.getTenantId();
        // 5. 验签成功后，查询 三方应用配置 对应的tenantId，获取该租户的系统管理员，以管理员身份登陆
        TenantDO tenant = tenantService.getRequiredTenant(tenantId);
        TenantContextHolder.setThirdAppId(thirdAppDO.getId());

        // 登陆成功后缓存token，下次请求直接返回
        Integer userType = getUserType().getValue();
        AuthLoginRespVO loginRespVO = createTokenAfterLoginSuccess(tenant.getContactUserId(), userType, tenant.getContactName(), LoginLogTypeEnum.LOGIN_API, tenantId, ClientChannelTypeEnum.THIRD.getCode());

        // 缓存token（计算token剩余过期时间）
        long durationSeconds = Duration.between(LocalDateTime.now(), loginRespVO.getExpiresTime()).getSeconds();
        if (durationSeconds > 10) {
            RedisUtils.set(key, JSONUtil.toJsonStr(loginRespVO), durationSeconds - 5);
        }
        return loginRespVO;
    }

    private void createLogoutLog(Long userId, Integer userType, Integer logType) {
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logType);
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(userType);
        if (ObjectUtil.equal(getUserType().getValue(), userType)) {
            reqDTO.setUsername(getUsername(userId));
        } else {
            reqDTO.setUsername(memberService.getMemberUserMobile(userId));
        }
        reqDTO.setUserAgent(StringUtils.defaultIfBlank(ServletUtils.getUserAgent(), "rpc"));
        reqDTO.setUserIp(StringUtils.defaultIfBlank(ServletUtils.getClientIP(), "0.0.0.0"));
        reqDTO.setResult(LoginResultEnum.SUCCESS.getResult());
        loginLogService.createLoginLog(reqDTO);
    }

    private String getUsername(Long userId) {
        if (userId == null) {
            return null;
        }
        AdminUserDO user = userService.getUserStore(userId);

        return user != null ? user.getUsername() : null;
    }

    private UserTypeEnum getUserType() {
        return UserTypeEnum.ADMIN;
    }

    @Override
    public void logout(List<Long> userIdList, UserTypeEnum userType, LoginLogTypeEnum logType) {
        // 跟据userId查询用户在线token 和 refreshToken 并删除
        int removeRow = oauth2TokenService.removeAccessTokenByUserIdList(userIdList);
        if (removeRow < 1) {
            return;
        }
        // 删除成功，则记录登出日志
        createLogoutLog(userIdList, userType, logType.getType());
    }

    private void createLogoutLog(List<Long> userIdList, UserTypeEnum userType, Integer logType) {

        Map<Long, String> userNameMap = getUserNameMap(userIdList, userType);

        List<LoginLogCreateReqDTO> loginLogCreateReqDTOList = new ArrayList<>();
        for (Long userId : userIdList) {
            LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
            reqDTO.setLogType(logType);
            reqDTO.setTraceId(TracerUtils.getTraceId());
            reqDTO.setUserId(userId);
            reqDTO.setUserType(userType.getValue());
            reqDTO.setUsername(userNameMap.get(userId));
            reqDTO.setUserAgent(StringUtils.defaultIfBlank(ServletUtils.getUserAgent(), "rpc"));
            reqDTO.setUserIp(StringUtils.defaultIfBlank(ServletUtils.getClientIP(), "0.0.0.0"));
            reqDTO.setResult(LoginResultEnum.SUCCESS.getResult());
            loginLogCreateReqDTOList.add(reqDTO);
        }

        loginLogService.createLoginLog(loginLogCreateReqDTOList);
    }

    /**
     * 获取用户名称
     *
     * @param userIdList 用户编号
     * @param userType   用户类型
     * @return 用户名称
     */
    private Map<Long, String> getUserNameMap(List<Long> userIdList, UserTypeEnum userType) {

        if (ObjectUtil.equal(getUserType().getValue(), userType)) {
            List<AdminUserDO> userList = userService.getUserList(userIdList);
            if (CollUtil.isEmpty(userList)) {
                return Maps.newHashMap();
            }
            return userList.stream().collect(Collectors.toMap(AdminUserDO::getId, AdminUserDO::getUsername));
        } else {
            List<MemberUserDO> memberUserDOList = memberUserService.getUserList(userIdList);
            if (CollUtil.isEmpty(memberUserDOList)) {
                return Maps.newHashMap();
            }
            return memberUserDOList.stream().collect(Collectors.toMap(MemberUserDO::getId, MemberUserDO::getNickname));
        }
    }
}
