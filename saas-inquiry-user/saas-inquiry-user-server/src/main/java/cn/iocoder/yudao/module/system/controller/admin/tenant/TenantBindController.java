package cn.iocoder.yudao.module.system.controller.admin.tenant;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthSmsSendReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantBindReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantBindRespVO;
import cn.iocoder.yudao.module.system.service.user.TenantBindBusinessService;
import com.xyy.saas.inquiry.constant.ValidateGroup.Add;
import com.xyy.saas.inquiry.constant.ValidateGroup.Update;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 门店")
@RestController
@RequestMapping("/system/tenant-bind")
@Validated
public class TenantBindController {

    @Resource
    private TenantBindBusinessService tenantBindBusinessService;

    /**
     * 后台绑定老荷叶机构号
     *
     * @param name
     * @return
     */

    @PostMapping("/old-hy-system")
    @Operation(summary = "后台绑定老荷叶机构号")
    public CommonResult<Long> bindOldHySystem(@RequestBody @Validated(value = {Add.class}) TenantBindReqVO bindReqVO) {
        return success(tenantBindBusinessService.bindOldHySystem(bindReqVO));
    }

    /**
     * 用户解绑老荷叶机构号
     *
     * @param name
     * @return
     */

    @PutMapping("/old-hy-unbind")
    @Operation(summary = "用户解绑老荷叶机构号")
    public CommonResult<Long> bindOldHyUnBind(@RequestParam(value = "tenantId", required = false) Long tenantId) {
        return success(tenantBindBusinessService.bindOldHyUnBind(tenantId));
    }


    @PostMapping("/send-bind-sms-code")
    @Operation(summary = "用户发送绑定验证码")
    public CommonResult<Boolean> senBindSmsCode(@RequestBody @Valid AuthSmsSendReqVO reqVO) {
        tenantBindBusinessService.senBindSmsCode(reqVO);
        return success(true);
    }

    /**
     * 门店绑定老荷叶机构号
     *
     * @param reqVO
     * @return
     */
    @PostMapping("/old-hy-user")
    @Operation(summary = "门店绑定老荷叶机构号")
    public CommonResult<TenantBindRespVO> bindOldHyUser(@RequestBody @Validated(value = {Update.class}) TenantBindReqVO reqVO) {
        return success(tenantBindBusinessService.bindOldHyUser(reqVO));
    }

}
