package cn.iocoder.yudao.module.member.service.auth;

import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlDeleteAccountReqVO;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.AppAuthLoginRespVO;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.AppAuthWeixinMiniAppLoginReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlBindAccountReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlBindAccountRespVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlBindTenantReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlBindTenantRespVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlTicketLoginReqVO;
import jakarta.validation.Valid;

/**
 * 会员的认证 Service 接口
 * <p>
 * 提供用户的账号密码登录、token 的校验等认证相关的功能
 *
 * <AUTHOR>
 */
public interface MemberAuthService {

    /**
     * 微信小程序的一键登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AppAuthLoginRespVO weixinMiniAppLogin(AppAuthWeixinMiniAppLoginReqVO reqVO);

    /**
     * 智慧脸登录
     *
     * @param reqVO
     * @return
     */
    ZhlBindTenantRespVO zhlBindTenant(@Valid ZhlBindTenantReqVO reqVO);

    /**
     * 智慧脸绑定账号
     *
     * @param reqVO
     * @return
     */
    ZhlBindAccountRespVO zhlBindAccount(@Valid ZhlBindAccountReqVO reqVO);

    /**
     * 智慧脸票据登录
     *
     * @param reqVO
     * @return
     */
    AppAuthLoginRespVO zhlTicketLogin(@Valid ZhlTicketLoginReqVO reqVO);

    /**
     * 智慧脸删除账号
     *
     * @param reqVO
     * @return
     */
    boolean zhlDeleteAccount(@Valid ZhlDeleteAccountReqVO reqVO);
}
