package com.xyy.saas.inquiry.enums.inquiry;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import com.xyy.saas.inquiry.pojo.StatusEnumDto;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 问诊处方流转状态
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum PrescriptionTransferStatusEnum implements IntArrayValuable {

    ALREADY(0, "已问诊", ""),

    WAITING(1, "待开方", "待开方中"),

    OPENED(2, "已开方", "医生已开方"),

    CLOSED(3, "处方关闭", "处方关闭"),

    TIMEOUT(4, "开方超时", "开方超时"),

    PENDING(5, "待审核", "处方审核中"),

    REVIEWED(6, "已审核", "审核通过"),

    REJECTED(7, "审核驳回", "审核不通过"),

    CANCELED(8, "已取消", "已取消");

    private final int status;

    private final String name;

    //mock 消息仅用于前期联调使用正式不会上线
    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PrescriptionTransferStatusEnum::getStatus).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static PrescriptionTransferStatusEnum fromStatusCode(int status) {
        return Arrays.stream(values())
            .filter(value -> value.getStatus() == status)
            .findFirst()
            .orElse(REVIEWED);
    }

    /**
     * 转换问诊处方状态
     *
     * @param inquiryStatusEnum      问诊状态
     * @param prescriptionStatusEnum 处方状态
     * @return
     */
    public static List<StatusEnumDto> convertInquiryPrescriptionStatusList(InquiryStatusEnum inquiryStatusEnum, PrescriptionStatusEnum prescriptionStatusEnum, InquiryBizTypeEnum inquiryBizTypeEnum) {
        // 医生取消开方
        if (Objects.equals(inquiryStatusEnum, InquiryStatusEnum.DOCTOR_CANCELED)) {
            return convertStatusEnums(List.of(ALREADY, CLOSED, PENDING, REVIEWED));
        }
        // 医生开方超时
        if (Objects.equals(inquiryStatusEnum, InquiryStatusEnum.TIMEOUT_CANCELED)) {
            return convertStatusEnums(List.of(ALREADY, TIMEOUT, PENDING, REVIEWED));
        }
        // 审核驳回
        if (Objects.equals(prescriptionStatusEnum, PrescriptionStatusEnum.APPROVAL_REJECTED)) {
            // 药店问诊流程节点- 已问诊> 已开方>待审核>审核驳回
            // 远程审方问诊流程节点- 已问诊>待审核>审核驳回
            return convertStatusEnums(ObjectUtil.equals(inquiryBizTypeEnum, InquiryBizTypeEnum.REMOTE_INQUIRY) ? List.of(ALREADY, PENDING, REJECTED) : List.of(ALREADY, OPENED, PENDING, REJECTED));
        }

        // 未知或者待开方
        PrescriptionTransferStatusEnum e2 = prescriptionStatusEnum == null || Objects.equals(prescriptionStatusEnum.getStatusCode(), PrescriptionStatusEnum.WAITING.getStatusCode())
            ? WAITING : OPENED;
        // 其他都是正常状态 :  1.患者排队中  | 2.医生开方-待审核 | 3.医生开方-审核中

        List<PrescriptionTransferStatusEnum> defaultNodeList = List.of(ALREADY, e2, PENDING, REVIEWED);

        List<PrescriptionTransferStatusEnum> remoteInquiryNodeList = Objects.equals(prescriptionStatusEnum, PrescriptionStatusEnum.CANCELED) ? List.of(ALREADY, PENDING, CANCELED) : List.of(ALREADY, PENDING, REVIEWED);

        return convertStatusEnums(ObjectUtil.equals(inquiryBizTypeEnum, InquiryBizTypeEnum.REMOTE_INQUIRY) ? remoteInquiryNodeList : defaultNodeList);
    }


    public static PrescriptionTransferStatusEnum convertInquiryPrescriptionStatus(InquiryStatusEnum inquiryStatusEnum, PrescriptionStatusEnum prescriptionStatusEnum) {
        if (Objects.equals(inquiryStatusEnum, InquiryStatusEnum.QUEUING)) { // 排队中
            return ALREADY;
        }
        if (Objects.equals(inquiryStatusEnum, InquiryStatusEnum.INQUIRING)
            || prescriptionStatusEnum == null) { // 已链接 || 处方为空
            return WAITING;
        }
        if (Objects.equals(inquiryStatusEnum, InquiryStatusEnum.DOCTOR_CANCELED)) { // 医生取消开方
            return CLOSED;
        }
        if (Objects.equals(inquiryStatusEnum, InquiryStatusEnum.TIMEOUT_CANCELED)) { // 医生开方超时
            return TIMEOUT;
        }
        if (Objects.equals(prescriptionStatusEnum, PrescriptionStatusEnum.WAITING)
            || Objects.equals(prescriptionStatusEnum, PrescriptionStatusEnum.WAIT_APPROVAL)
            || Objects.equals(prescriptionStatusEnum, PrescriptionStatusEnum.APPROVAL_ING)) { // 待开方 或者 医生开方待审核
            return PENDING;
        }
        if (Objects.equals(prescriptionStatusEnum, PrescriptionStatusEnum.APPROVAL)) { // 已审核
            return REVIEWED;
        }

        if (Objects.equals(prescriptionStatusEnum, PrescriptionStatusEnum.CANCELED)) { // 问诊完成，处方取消
            return CANCELED;
        }

        return REJECTED;
    }


    public static List<StatusEnumDto> convertStatusEnums(List<PrescriptionTransferStatusEnum> enums) {
        return enums.stream().map(e -> new StatusEnumDto(e.getStatus(), e.getName(), e.getDesc())).toList();
    }

    public static StatusEnumDto convertStatusEnum(PrescriptionTransferStatusEnum e) {
        return new StatusEnumDto(e.getStatus(), e.getName(), e.getDesc());
    }

    /**
     * 是否是x图标类型
     *
     * @param status
     * @return
     */
    public static boolean isCloseType(Integer status) {
        return List.of(CLOSED.getStatus(), TIMEOUT.getStatus(), REJECTED.getStatus(), CANCELED.getStatus()).contains(status);
    }

}

