package com.xyy.saas.inquiry.util;

import lombok.extern.slf4j.Slf4j;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: xucao
 * @Date: 2025/2/21 17:42
 * @Description: 线程池执行器
 */
@Slf4j
public class ThreadPoolManager {

    private static final int CORE_POOL_SIZE = Runtime.getRuntime().availableProcessors();
    private static final int MAX_POOL_SIZE = CORE_POOL_SIZE * 2;
    private static final int QUEUE_CAPACITY = 1000;
    private static final long KEEP_ALIVE_TIME = 60L;

    // 使用静态内部类实现线程安全
    private static class ExecutorHolder {

        private static final ThreadPoolExecutor INSTANCE = new ThreadPoolExecutor(
            CORE_POOL_SIZE,
            MAX_POOL_SIZE,
            KEEP_ALIVE_TIME,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(QUEUE_CAPACITY),
            new NamedThreadFactory("global-pool"),
            new LogAndAbortPolicy()
        );

        static {
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                INSTANCE.shutdown();
                try {
                    if (!INSTANCE.awaitTermination(30, TimeUnit.SECONDS)) {
                        INSTANCE.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }));
        }
    }

    // 自定义线程工厂（带命名）
    private static class NamedThreadFactory implements ThreadFactory {

        private final String namePrefix;
        private final AtomicInteger threadNumber = new AtomicInteger(1);

        NamedThreadFactory(String namePrefix) {
            this.namePrefix = "pool-" + namePrefix + "-thread-";
        }

        public Thread newThread(Runnable r) {
            return new Thread(r, namePrefix + threadNumber.getAndIncrement());
        }
    }

    // 自定义拒绝策略（带日志记录）
    private static class LogAndAbortPolicy implements RejectedExecutionHandler {

        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            log.error("Task " + r.toString() + " rejected from "
                + executor.toString() + ". Active: " + executor.getActiveCount()
                + ", Queue: " + executor.getQueue().size());
            throw new RejectedExecutionException("Task " + r.toString() + " rejected from "
                + executor.toString() + ". Active: " + executor.getActiveCount()
                + ", Queue: " + executor.getQueue().size());
        }
    }

    // 对外暴露的静态方法
    public static void execute(Runnable task) {
        ExecutorHolder.INSTANCE.execute(ThreadPoolExecutorMdcWrapper.wrap(task));
    }

    public static <T> Future<T> submit(Callable<T> task) {
        return ExecutorHolder.INSTANCE.submit(task);
    }

    // 防止实例化
    private ThreadPoolManager() {
    }
}
