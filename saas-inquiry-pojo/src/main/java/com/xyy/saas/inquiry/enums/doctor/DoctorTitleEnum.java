package com.xyy.saas.inquiry.enums.doctor;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @ClassName：DoctorTitleEnum
 * @Author: xucao
 * @Date: 2024/11/11 16:58
 * @Description: 医生职称枚举
 */
@Getter
@RequiredArgsConstructor
public enum DoctorTitleEnum {
    RESIDENT_DOCTOR(13, "住院医师"),
    PHARMACY_TECHNICIAN(12, "药士"),
    PHARMACIST(11, "药师"),
    CHIEF_PHARMACIST(10, "主管药师"),
    DEPUTY_CHIEF_PHARMACIST(9, "副主任药师"),
    CHIEF_PHARMACIST_DIRECTOR(8, "主任药师"),
    MEDICAL_TECHNICIAN(6, "医士"),
    DOCTOR(4, "医师"),
    ATTENDING_DOCTOR(3, "主治医师"),
    DEPUTY_CHIEF_DOCTOR(2, "副主任医师"),
    CHIEF_DOCTOR(1, "主任医师");

    private final int code;
    private final String desc;


    public static DoctorTitleEnum fromCode(int code) {
        for (DoctorTitleEnum title : values()) {
            if (title.getCode() == code) {
                return title;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }
}
