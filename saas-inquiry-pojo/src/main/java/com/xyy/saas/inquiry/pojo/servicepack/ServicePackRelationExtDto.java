package com.xyy.saas.inquiry.pojo.servicepack;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/06 14:54
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "服务包扩展信息ext")
public class ServicePackRelationExtDto implements Serializable {

    @Schema(description = "网络配置code")
    private String networkCode;

    @Schema(description = "服务包变更时间")
    private LocalDateTime servicePackChangeTime;

    @Schema(description = "目录变更事件")
    private LocalDateTime catalogChangeTime;


}
