package com.xyy.saas.inquiry.enums.transmitter;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum OrganTypeEnum {
    MEDICAL_INSURANCE(1, "医保", "医保服务包"),
    DRUG_SUPERVISION(2, "药监", "药监服务包"),
    INTERNET_SUPERVISION(3, "互联网监管", "互联网监管服务包"),
    ERP(4, "ERP对接", "ERP服务包"),
    HIS(5, "HIS对接", "HIS服务包"),
    SELF(6, "自营渠道", "自营服务包"),
    OTHER(999, "其他", "其他服务包");

    private final Integer code;
    private final String desc;
    private final String servicePackDesc;

    public static String getDesc(Integer code) {
        for (OrganTypeEnum organTypeEnum : OrganTypeEnum.values()) {
            if (organTypeEnum.getCode().equals(code)) {
                return organTypeEnum.getDesc();
            }
        }
        return null;
    }

    public static String getServicePackDesc(Integer code) {
        for (OrganTypeEnum organTypeEnum : OrganTypeEnum.values()) {
            if (organTypeEnum.getCode().equals(code)) {
                return organTypeEnum.getServicePackDesc();
            }
        }
        return "";
    }
} 