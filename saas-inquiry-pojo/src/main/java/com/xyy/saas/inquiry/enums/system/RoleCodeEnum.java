package com.xyy.saas.inquiry.enums.system;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.util.object.ObjectUtils;
import cn.iocoder.yudao.module.system.enums.permission.RoleTypeEnum;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

/**
 * 角色标识枚举
 */
@Getter
@AllArgsConstructor
public enum RoleCodeEnum {

    /**
     * 运营后台管理员
     */
    SUPER_ADMIN("super_admin", "超级管理员", RoleTypeEnum.SYSTEM),
    MANAGE_ADMIN("manage_admin", "运营后台管理员", RoleTypeEnum.SYSTEM), // 运营后台管理员
    DOCTOR("doctor", "医生", RoleTypeEnum.SYSTEM),

    STORE_ADMIN("store_admin", "门店管理员", RoleTypeEnum.SYSTEM), // 门店管理员
    STORE_EMPLOYEE("store_employee", "店员", RoleTypeEnum.SYSTEM), // 店员
    PHARMACIST("pharmacist", "药师", RoleTypeEnum.SYSTEM),
    HOSPITAL_EMPLOYEE("hospital_employee", "医院员工", RoleTypeEnum.SYSTEM), // 医院员工
    ZHL_EMPLOYEE("zhl_employee", "智慧脸员工", RoleTypeEnum.SYSTEM), // 智慧脸员工

    /**
     * 问诊系统默认角色
     */
    CHECK("check", "核对", RoleTypeEnum.CUSTOM),
    ALLOCATION("allocation", "调配", RoleTypeEnum.CUSTOM),
    DISPENSING("dispensing", "发药", RoleTypeEnum.CUSTOM),

    /**智慧脸*/
//    ZHL_ADMIN("zhl_admin", "管理员"), // 智慧脸门店管理员

    ;

    /**
     * 角色编码
     */
    private final String code;
    /**
     * 名字
     */
    private final String name;

    private final RoleTypeEnum roleType;


    public static boolean isSuperAdmin(String code) {
        return ObjectUtils.equalsAny(code, SUPER_ADMIN.getCode());
    }

    public static boolean isStoreAdmin(String code) {
        return ObjectUtils.equalsAny(code, STORE_ADMIN.getCode());
    }

    public static RoleCodeEnum forCode(String code) {
        for (RoleCodeEnum codeEnum : values()) {
            if (StringUtils.equals(code, codeEnum.getCode())) {
                return codeEnum;
            }
        }
        return null;
    }

    /**
     * 获取问诊角色信息
     *
     * @return 问诊角色
     */
    public static List<RoleCodeEnum> listWzRole() {
        return Stream.of(PHARMACIST, CHECK, ALLOCATION, DISPENSING).collect(Collectors.toList());
    }


    /**
     * 获取问诊角色信息
     *
     * @return 问诊角色
     */
    public static List<RoleCodeEnum> listWzCadRole() {
        return Stream.of(CHECK, ALLOCATION, DISPENSING).collect(Collectors.toList());
    }

    /**
     * 获取问诊核对/发药/调配信息
     *
     * @return 问诊角色
     */
    public static Set<String> listWzCadRoleCodes() {
        return Stream.of(PHARMACIST, CHECK, ALLOCATION, DISPENSING).map(RoleCodeEnum::getCode).collect(Collectors.toSet());
    }

    /**
     * 是否有药师角色
     *
     * @param roleCodeList 角色 codes
     */
    public static boolean pharmacist(List<String> roleCodeList) {
        return CollUtil.isNotEmpty(roleCodeList) && roleCodeList.contains(RoleCodeEnum.PHARMACIST.getCode());
    }

    /**
     * 是否有医生角色
     *
     * @param roleCodeList 角色 codes
     */
    public static boolean doctor(List<String> roleCodeList) {
        return CollUtil.isNotEmpty(roleCodeList) && roleCodeList.contains(RoleCodeEnum.DOCTOR.getCode());
    }

    /**
     * 是否店铺管理员
     *
     * @param roleCodeList 角色 codes
     */
    public static boolean storeAdmin(List<String> roleCodeList) {
        return CollUtil.isNotEmpty(roleCodeList) && roleCodeList.contains(RoleCodeEnum.STORE_ADMIN.getCode());
    }

    /**
     * 是否医院员工
     *
     * @param roleCodeList 角色 codes
     */
    public static boolean hospitalEmployee(List<String> roleCodeList) {
        return CollUtil.isNotEmpty(roleCodeList) && roleCodeList.contains(RoleCodeEnum.HOSPITAL_EMPLOYEE.getCode());
    }

    public static boolean isSystemRole(RoleCodeEnum roleCode) {
        return Arrays.stream(values()).filter(roleCodeEnum -> roleCodeEnum.getRoleType() == RoleTypeEnum.SYSTEM).toList().contains(roleCode);
    }

    public static boolean isHospitalEmployee(List<String> roleCodes) {
        return !CollectionUtils.isEmpty(roleCodes) && roleCodes.contains(HOSPITAL_EMPLOYEE.getCode());
    }
}
