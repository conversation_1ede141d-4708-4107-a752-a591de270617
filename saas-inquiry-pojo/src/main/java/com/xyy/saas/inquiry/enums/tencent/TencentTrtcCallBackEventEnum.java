package com.xyy.saas.inquiry.enums.tencent;

import lombok.Getter;
import java.util.Arrays;

/**
 * @Author: xucao
 * @Description:腾讯TRTC回调事件枚举类
 */
@Getter
public enum TencentTrtcCallBackEventEnum {
    EVENT_TYPE_ENTER_ROOM(103, "进入房间回调"),
    EVENT_TYPE_EXIT_ROOM(104, "退出房间回调"),
    EVENT_TYPE_STREAM_INGEST_START(701, "输入在线媒体流开始回调");


    private final int eventType;

    private final String eventDesc;

    TencentTrtcCallBackEventEnum(int eventType, String eventDesc) {
        this.eventType = eventType;
        this.eventDesc = eventDesc;
    }

    public static TencentTrtcCallBackEventEnum fromCode(int eventType) {
        return Arrays.stream(values()).filter(item -> item.eventType == eventType).findFirst().orElse(null);
    }
}
