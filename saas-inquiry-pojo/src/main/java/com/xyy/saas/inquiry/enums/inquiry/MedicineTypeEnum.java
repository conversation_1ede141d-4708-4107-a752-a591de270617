package com.xyy.saas.inquiry.enums.inquiry;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * @Desc 用药类型
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/29 下午8:27
 */
@Getter
@RequiredArgsConstructor
public enum MedicineTypeEnum implements IntArrayValuable {

    ASIAN_MEDICINE(0, "西药", "medicineType"),

    CHINESE_MEDICINE(1, "中药", "medicineType"),
    ;

    private final int code;

    private final String name;

    private final String fieldName; // 用于绘制处方笺常量字段

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(MedicineTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static MedicineTypeEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(ASIAN_MEDICINE);
    }

    public static String getMedicineTypeName(int code) {
        return fromCode(code).getName();
    }

    /**
     * 获取商品大类
     * @param medicineType
     * @return
     */
    public static String getSpuCategory(Integer medicineType) {
        if (medicineType == MedicineTypeEnum.ASIAN_MEDICINE.getCode()) {
            return "普通药品";
        } else if (medicineType == MedicineTypeEnum.CHINESE_MEDICINE.getCode()) {
            return "中药";
        }
        return null;
    }

    /**
     * 获取医保目录类别
     * @param medicineType
     * @return
     */
    public static List<String> getProjectTypeList(Integer medicineType) {
        if (medicineType == MedicineTypeEnum.ASIAN_MEDICINE.getCode()) {
            return List.of("西药", "中成药");
        } else if (medicineType == MedicineTypeEnum.CHINESE_MEDICINE.getCode()) {
            return List.of("中药饮片");
        }
        return null;
    }

}
