package com.xyy.saas.inquiry.enums.system;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @Desc 系统业务类型
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/29 下午7:51
 */
@Getter
@RequiredArgsConstructor
public enum ClientTypeEnum implements IntArrayValuable {

    APP(0, "app"),
    //快速问诊
    WEB(1, "web");

    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(ClientTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static ClientTypeEnum fromCode(Integer code) {
        return Arrays.stream(values())
            .filter(type -> Objects.equals(type.getCode(), code))
            .findFirst()
            .orElse(APP);
    }

}